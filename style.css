/* -------- General style ------------*/

html, body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  font-family: "DejaVu Sans Mono", "DejaVuSansMono", monospace;
  background-color: black;
}

.shifted {
  margin-right: 5px;
}

/* App colors & Fonts */

:root {
  --marvel-red: #ec1d24;
  --marvel-red-light: #ff4f56;
  --marvel-red-dark: #ba0000;

  /* <PERSON> House Colors */
  --gryffindor-gold: #d4af37;
  --gryffindor-red: #8b0000;
  --slytherin-green: #2d5016;
  --slytherin-silver: #c0c0c0;
  --ravenclaw-blue: #0e1a40;
  --ravenclaw-bronze: #cd7f32;
  --hufflepuff-yellow: #ffdb00;
  --hufflepuff-black: #000000;

  /* <PERSON> Theme Colors */
  --hp-magic-purple: #800080;
  --hp-dark-arts: #1a1a1a;
  --hp-forest-green: #228b22;
  --hp-parchment: #f4e4bc;
}
.red {
  color: var(--marvel-red);
}
.lightred {
  color: var(--marvel-red-light);
}

@font-face {
  font-family: "ImpactLoad";
  src: url(./fonts/impact.ttf) format("truetype");
}
@font-face {
  font-family: "DejaVuSansMono";
  src: url(./fonts/LiberationMono-Regular.ttf) format("truetype");
}

/* Discrete selected texts */

::selection {
  background-color: #000;
}
::-moz-selection {
  background-color: #000;
}

input[type="search"]::selection {
  background-color: #000;
}
input[type="search"]::-moz-selection {
  background-color: #000;
}

/* Buttons */

button {
  padding: 0;
  margin: 2px;
  width: 38px;
  height: 38px;
  background-color: #333;
  font-size: 15px;
  font-weight: bold;
  text-indent: 0px;
  text-align: center;
  box-sizing: border-box;
  outline: none;
  cursor: pointer;
  border: 1px solid #444;
}

button.selected {
  background-color: var(--marvel-red);
  border-color: #555;
}

button.close-button {
  position: fixed;
  top: 0;
  right: 2px;
  margin: 2px!important;
}

button img {
  width: 24px;
  height: 24px;
  margin-top: 3px;
}


/* Hover tooltips on actions */

#tooltip {
  display: none;
  position: fixed;
  background-color: #555;
  border: 1px solid #777;
  border-radius: 3px;
  padding: 4px 6px;
  font-size: 13px;
  font-weight: bold;
  color: white;
  z-index: 25;
  min-width: 80px;
  max-width: 200px;
  text-align: center;
}
#tooltip span {
  display: inline-block;
}

/* Missing JS/WebGL disclaimers */

#webgl-disclaimer, #noscript {
  z-index: 10;
  opacity: 0.8;
}
#webgl-disclaimer {
  display: none;
}
#webgl-disclaimer p, #noscript p {
  position: relative;
  width: 66%;
  top: 35%;
  left: 17%;
  text-align: center;
  font-size: 22px;
  color: #999;
}


/* -------- Main sidebar ------------*/

#sidebar {
  width: 500px;
  height: 100%;
  margin: 0;
  margin-right: 2px;
  overflow: hidden;
  font-size: 14px;
  background-color: #222;
  color: #AAA;
  float: left;
  text-align: center;
  box-shadow: 2px 0 5px #000;
  z-index: 1;
  position: absolute;
}
#sidebar a, #sidebar a:visited, #comics-bar a, #comics-bar a:visited {
  color: #af4f4f;
}

/* Logo & Title */

#header {
  padding-bottom: 10px;
}

.reset-graph {
  cursor: pointer;
}

.logo {
  float: left;
  padding: 3px 0 0 4px;
  height: 47px;
}

h1 {
  font-size: 32px;
  background-color: var(--marvel-red);
  color: white;
  padding: 5px 0;
  margin: 0 0 9px 0;
}
.marvel {
  font-family: "Impact", "ImpactLoad", monospace;
  font-weight: normal;
  font-size: 36px;
  letter-spacing: -1px;
  transform: scaleY(1.2);
  transform-origin: middle;
  display: inline-block;
  padding-right: 10px;
  vertical-align: top;
}

h2 {
  color: #AAA;
  font-size: 17px;
  margin: 0 6px;
}
#title {
  display: inline;
}
#node-label {
  display: none;
  margin-top: 3px;
}

/* Text info zone */

.sidebar-text {
  clear: both;
  display: none;
  margin: 0;
  padding: 0;
  overflow-y: auto;
  line-height: 16px;
}
#explanations {
  border-top: 1px dashed #444;
  border-bottom: 1px dashed #444;
  opacity: 0;
  -webkit-transition: opacity 0.75s ease-in-out;
  -moz-transition: opacity 0.75s ease-in-out;
  -o-transition: opacity 0.75s ease-in-out;
  transition: opacity 0.75s ease-in-out;
}
#explanations p {
  margin: 7px 25px;
}

#order {
  min-width: 20px;
  display: inline-block;
}
#clusters-legend b span {
  white-space: nowrap;
}

/* EPIC Enhanced Character Details Panel - FULLY SCROLLABLE */
#node-details {
  text-align: left;
  height: calc(100vh - 120px);
  max-height: none;
  padding: 0;
  border: 2px solid;
  border-image: linear-gradient(45deg, #ffd700, #ff6b6b, #4ecdc4, #ffd700) 1;
  background: linear-gradient(135deg,
    rgba(26, 26, 46, 0.95) 0%,
    rgba(22, 33, 62, 0.95) 30%,
    rgba(16, 21, 46, 0.95) 70%,
    rgba(10, 10, 30, 0.95) 100%);
  border-radius: 16px;
  margin: 8px;
  overflow-y: auto;
  overflow-x: hidden;
  box-shadow:
    0 0 30px rgba(78, 205, 196, 0.3),
    0 0 60px rgba(255, 215, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
}

/* Custom Scrollbar for Side Panel */
#node-details::-webkit-scrollbar {
  width: 8px;
}

#node-details::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

#node-details::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #4ecdc4, #ffd700);
  border-radius: 4px;
  box-shadow: 0 0 10px rgba(78, 205, 196, 0.3);
}

#node-details::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #5fd8d1, #ffdd33);
  box-shadow: 0 0 15px rgba(78, 205, 196, 0.5);
}

#node-details::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #ffd700, #ff6b6b, #4ecdc4, #ffd700);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

#node-img, #comic-img {
  max-width: 100%;
  max-height: 280px;
  cursor: pointer;
  display: block;
  margin: 0;
  border-radius: 16px 16px 0 0;
  object-fit: cover;
  width: 100%;
  transition: transform 0.3s ease;
  filter: brightness(1.1) contrast(1.1);
}

#node-img:hover, #comic-img:hover {
  transform: scale(1.02);
  filter: brightness(1.2) contrast(1.2);
}

#node-extra {
  margin: 0;
  padding: 0;
  color: #e0e0e0;
  font-size: 16px;
  line-height: 1.7;
  min-height: calc(100vh - 200px);
}

/* EPIC Character Header Section */
.character-panel-header {
  background: linear-gradient(135deg,
    rgba(255, 215, 0, 0.15) 0%,
    rgba(78, 205, 196, 0.1) 50%,
    rgba(255, 107, 107, 0.1) 100%);
  padding: 25px 20px;
  border-bottom: 3px solid;
  border-image: linear-gradient(90deg, #ffd700, #4ecdc4, #ff6b6b) 1;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.character-panel-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent);
  animation: sweep 4s ease-in-out infinite;
}

@keyframes sweep {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: 100%; }
}

.character-panel-header h3 {
  margin: 0 0 12px 0;
  font-size: 1.8em;
  font-weight: 900;
  color: #fff;
  background: linear-gradient(45deg, #ffd700, #ff6b6b, #4ecdc4, #ffd700);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease-in-out infinite;
  text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
  letter-spacing: 1px;
  text-transform: uppercase;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.character-archetype-badge {
  display: inline-block;
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.3), rgba(78, 205, 196, 0.1));
  color: #4ecdc4;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.95em;
  font-weight: bold;
  text-transform: uppercase;
  border: 2px solid rgba(78, 205, 196, 0.5);
  box-shadow: 0 0 15px rgba(78, 205, 196, 0.3);
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.character-archetype-badge:hover {
  transform: scale(1.05);
  box-shadow: 0 0 25px rgba(78, 205, 196, 0.5);
}

/* EPIC Character Stats Section */
.character-stats-section {
  padding: 25px 20px;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.05) 0%,
    rgba(78, 205, 196, 0.03) 50%,
    rgba(255, 107, 107, 0.03) 100%);
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.character-stats-section::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, #ffd700, #4ecdc4, #ff6b6b);
}

.character-stats-section h4 {
  margin: 0 0 20px 0;
  color: #4ecdc4;
  font-size: 1.4em;
  font-weight: 900;
  text-transform: uppercase;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.character-stats-section h4::before {
  content: '📊';
  font-size: 1.2em;
  filter: drop-shadow(0 0 10px rgba(78, 205, 196, 0.5));
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 15px 0;
  padding: 12px 15px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 10px;
  border-left: 3px solid transparent;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-row::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 3px;
  height: 100%;
  background: linear-gradient(180deg, #ffd700, #4ecdc4);
  transition: width 0.3s ease;
}

.stat-row:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(5px);
}

.stat-row:hover::before {
  width: 100%;
  opacity: 0.1;
}

.stat-label {
  color: #bbb;
  font-size: 1.05em;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  color: #fff;
  font-weight: 900;
  font-size: 1.2em;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
  padding: 4px 12px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
}

.stat-value.highlight {
  color: #ffd700;
  background: rgba(255, 215, 0, 0.2);
  box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

.stat-value.connections {
  color: #ff6b6b;
  background: rgba(255, 107, 107, 0.2);
  box-shadow: 0 0 15px rgba(255, 107, 107, 0.3);
}

.stat-value.importance {
  color: #4ecdc4;
  background: rgba(78, 205, 196, 0.2);
  box-shadow: 0 0 15px rgba(78, 205, 196, 0.3);
}

/* EPIC Character Description Section - ENHANCED */
.character-description-section {
  padding: 30px 24px;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.06) 0%,
    rgba(255, 215, 0, 0.04) 100%);
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
  position: relative;
  min-height: 300px;
}

.character-description-section::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, #ff6b6b, #ffd700);
}

.character-description-section h4 {
  margin: 0 0 24px 0;
  color: #4ecdc4;
  font-size: 1.6em;
  font-weight: 900;
  text-transform: uppercase;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.character-description-section h4::before {
  content: '📖';
  font-size: 1.2em;
  filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.5));
}

.character-description-text {
  color: #e8e8e8;
  line-height: 1.8;
  font-size: 1.15em;
  background: rgba(255, 255, 255, 0.05);
  padding: 24px;
  border-radius: 12px;
  border-left: 4px solid #ffd700;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  min-height: 200px;
  max-height: none;
  overflow-y: auto;
  text-align: justify;
  word-spacing: 0.1em;
}

/* EPIC Character Chapters Section */
.character-chapters-section {
  padding: 25px 20px;
  background: linear-gradient(135deg,
    rgba(255, 215, 0, 0.03) 0%,
    rgba(78, 205, 196, 0.02) 100%);
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

.character-chapters-section::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, #4ecdc4, #ffd700);
}

.character-chapters-section h4 {
  margin: 0 0 20px 0;
  color: #4ecdc4;
  font-size: 1.4em;
  font-weight: 900;
  text-transform: uppercase;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.character-chapters-section h4::before {
  content: '📚';
  font-size: 1.2em;
  filter: drop-shadow(0 0 10px rgba(78, 205, 196, 0.5));
}

.chapters-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 15px;
}

.chapter-badge {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.3), rgba(255, 215, 0, 0.1));
  color: #ffd700;
  padding: 8px 14px;
  border-radius: 16px;
  font-size: 0.95em;
  font-weight: 900;
  border: 2px solid rgba(255, 215, 0, 0.5);
  box-shadow: 0 0 15px rgba(255, 215, 0, 0.2);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.chapter-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.chapter-badge:hover {
  transform: scale(1.1);
  box-shadow: 0 0 25px rgba(255, 215, 0, 0.4);
}

.chapter-badge:hover::before {
  left: 100%;
}

/* EPIC Character Group Section */
.character-group-section {
  padding: 25px 20px;
  background: linear-gradient(135deg,
    rgba(255, 107, 107, 0.03) 0%,
    rgba(255, 255, 255, 0.02) 100%);
  position: relative;
}

.character-group-section::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, #ff6b6b, #4ecdc4);
}

.character-group-section h4 {
  margin: 0 0 20px 0;
  color: #4ecdc4;
  font-size: 1.4em;
  font-weight: 900;
  text-transform: uppercase;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.character-group-section h4::before {
  content: '👥';
  font-size: 1.2em;
  filter: drop-shadow(0 0 10px rgba(255, 107, 107, 0.5));
}

.group-badge {
  display: inline-block;
  padding: 12px 20px;
  border-radius: 20px;
  font-size: 1.1em;
  font-weight: 900;
  border: 2px solid;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.group-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.group-badge:hover {
  transform: scale(1.05);
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
}

.group-badge:hover::before {
  left: 100%;
}

/* Relationship Details Styling */
.relationship-details {
  padding: 15px 20px;
  background: rgba(255, 255, 255, 0.02);
}

.relationship-details h4 {
  color: #4ecdc4;
  margin: 0 0 15px 0;
  font-size: 1.1em;
  font-weight: bold;
}

.relationship-summary {
  background: rgba(255, 255, 255, 0.05);
  padding: 15px;
  border-radius: 8px;
  border-left: 3px solid #ffd700;
  margin: 10px 0;
  color: #d0d0d0;
  line-height: 1.5;
}

.relationship-characters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 15px 0;
  padding: 12px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
}

.relationship-character {
  text-align: center;
  flex: 1;
}

.relationship-character .char-name {
  font-weight: bold;
  color: #fff;
  margin-bottom: 5px;
}

.relationship-character .char-role {
  color: #4ecdc4;
  font-size: 0.85em;
}

.relationship-arrow {
  color: #ff6b6b;
  font-size: 1.5em;
  margin: 0 15px;
}

.relationship-strength {
  display: inline-block;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 0.8em;
  font-weight: bold;
  margin: 5px 0;
}

.relationship-strength.strong {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.relationship-strength.moderate {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
}

.relationship-strength.weak {
  background: rgba(158, 158, 158, 0.2);
  color: #9e9e9e;
}

/* EPIC Enhanced scrollbar for side panel */
#node-details::-webkit-scrollbar {
  width: 12px;
}

#node-details::-webkit-scrollbar-track {
  background: linear-gradient(180deg,
    rgba(26, 26, 46, 0.8),
    rgba(22, 33, 62, 0.8));
  border-radius: 6px;
  border: 1px solid rgba(78, 205, 196, 0.2);
}

#node-details::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #ffd700, #4ecdc4, #ff6b6b);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 0 10px rgba(78, 205, 196, 0.3);
}

#node-details::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #ffed4e, #5ef0e1, #ff8a8a);
  box-shadow: 0 0 20px rgba(78, 205, 196, 0.5);
}

/* Epic glow effects for the entire panel */
#node-details:hover {
  box-shadow:
    0 0 40px rgba(78, 205, 196, 0.4),
    0 0 80px rgba(255, 215, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Pulse animation for important elements */
@keyframes pulse {
  0%, 100% {
    box-shadow: 0 0 15px rgba(78, 205, 196, 0.3);
  }
  50% {
    box-shadow: 0 0 25px rgba(78, 205, 196, 0.6);
  }
}

.stat-value.importance {
  animation: pulse 2s ease-in-out infinite;
}

/* Epic hover effects for sections */
.character-stats-section:hover,
.character-description-section:hover,
.character-chapters-section:hover,
.character-group-section:hover {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.08) 0%,
    rgba(78, 205, 196, 0.05) 50%,
    rgba(255, 107, 107, 0.05) 100%);
  transform: translateX(3px);
  transition: all 0.3s ease;
}

/* View more buttons */

.button {
  display: inline-block;
  cursor: pointer;
  font-weight: bold;
  font-size: 16px;
  margin: 10px 0 10px 0!important;
  text-transform: uppercase;
  line-height: 20px;
}
.button > span {
  width: 170px;
  background-color: var(--marvel-red);
  color: #DDD!important;
  border: 1px solid #444;
  border-radius: 3px;
  padding: 5px;
}

.button.selected, #choices.selected {
  opacity: 0.35;
  cursor: progress;
  -webkit-transition: 0.35s ease-in-out;
  -moz-transition: 0.35s ease-in-out;
  -o-transition: 0.35s ease-in-out;
  transition: 0.35s ease-in-out;
}
.button.selected.left, .button.selected.right {
  opacity: 1;
}

/* Switch buttons */

h5 {
  font-size: 15px;
  margin: 0;
  margin-bottom: 8px;
}

#choices {
  position: absolute;
  bottom: 36px;
  width: 300px;
  height: 84px;
  padding: 8px 0 0 0;
  margin: 0;
}
.network-choice {
  display: flex;
  justify-content: center;
  position:relative;
  width: 100%;
  margin-bottom: 10px;
  height: 28px;
}

/* Network Description Styles */
.network-description {
  padding: 10px 15px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  margin-top: 10px;
  font-size: 11px;
  line-height: 1.4;
}

.network-description p {
  margin: 0;
  font-style: italic;
  color: #bdc3c7;
}

.theme-tag {
  background-color: var(--marvel-red);
  color: white;
  padding: 2px 6px;
  border-radius: 2px;
  font-size: 10px;
  font-weight: bold;
}

/* Relationship details styling */
.relationship-details {
  padding: 10px 0;
}

.relationship-details h3 {
  color: #e74c3c;
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #34495e;
  padding-bottom: 8px;
}

.relationship-details p {
  margin: 8px 0;
  line-height: 1.4;
}

.relationship-details strong {
  color: #ecf0f1;
  font-weight: 600;
}

.relationship-details ul {
  margin: 8px 0 8px 20px;
  padding: 0;
}

.relationship-details li {
  margin: 6px 0;
  line-height: 1.4;
  color: #bdc3c7;
}

/* Dataset Selector Styling */
.dataset-selector {
  margin: 8px 0 12px 0;
}

.dataset-dropdown {
  width: 100%;
  padding: 8px 12px;
  background-color: #2c3e50;
  border: 1px solid #444;
  border-radius: 4px;
  color: #ecf0f1;
  font-family: "DejaVu Sans Mono", "DejaVuSansMono", monospace;
  font-size: 14px;
  cursor: pointer;
  outline: none;
  transition: all 0.3s ease;
}

.dataset-dropdown:hover {
  border-color: var(--marvel-red);
  background-color: #34495e;
}

.dataset-dropdown:focus {
  border-color: var(--marvel-red);
  box-shadow: 0 0 5px rgba(231, 76, 60, 0.3);
}

.dataset-dropdown option {
  background-color: #2c3e50;
  color: #ecf0f1;
  padding: 8px;
}

#choices .network-choice:last-child {
  margin-bottom: 0;
}
#choices .network-choice:first-child {
  margin-top: 0;
}

.network-switch-label, .toggle {
  height: 100%;
  border-radius: 3px;
}
.network-switch-label {
  display: block;
  width: 236px;
  background-color: #000;
  border-radius: 3px;
  position: relative;
  cursor: pointer;
}
.toggle {
  position: absolute;
  width: 116px;
  background-color: var(--marvel-red);
  transition: .3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  margin: -2px 0 0 0px;
  border: 1px solid #444;
  height: 30px
}

.names {
  font-size: 90%;
  font-weight: bolder;
  width: 100%;
  height: 100%;
  position: absolute;
  display: flex;
  justify-content: space-around;
  align-items: center;
  align-content: center;
  user-select: none;
}
.names > div {
  font-size: 16px;
  font-weight: bold;
  text-transform: uppercase;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.left, .right{
  width: 50%;
  text-align: center;
}
.left {
  color: white;
}
.right {
  color: var(--marvel-red);
}

[type="checkbox"] {
  display: none;
}
[type="checkbox"]:disabled + .network-switch-label {
  cursor: progress;
}
[type="checkbox"]:checked + .network-switch-label .toggle {
  transform: translateX(100%);
}
[type="checkbox"]:checked + .network-switch-label .left {
  color: var(--marvel-red);
}
[type="checkbox"]:checked + .network-switch-label .right {
  color: white;
}

/* Credits & Help Footer */

#credits {
  position: absolute;
  bottom: 0px;
  width: 300px;
  line-height: 13px;
  border-top: 1px dashed #444;
  background-color: #222;
  z-index: 2;
}
#credits p {
  font-size: 12px;
  padding: 3px 0px 0 34px;
  margin: 0;
}
#credits p:last-child {
  padding-bottom: 3px;
}

#help {
  position: absolute;
  left: 0;
  margin: 4px;
  width: 28px;
  height: 28px;
  background-color: var(--marvel-red)!important;
  color: white;
  font-family: Arial;
  font-size: 20px;
  font-weight: bold;
  text-indent: 0px;
  text-align: center;
  cursor: pointer;
  box-sizing: border-box;
  display: flex;
  align-content: center;
  justify-content: center;
  align-items: center;
  border: 1px solid #444;
  border-radius: 20px;
}


/* -------- Chapter Selector - Professional Design ------------*/

#chapter-selector-container {
  position: absolute;
  left: 302px;
  bottom: 0;
  width: calc(100% - 598px);
  margin-left: -2px;
  height: 120px;
  float: left;
  background: linear-gradient(180deg, #2a2a2a 0%, #1f1f1f 50%, #1a1a1a 100%);
  color: #AAA;
  text-align: center;
  box-shadow:
    0px -4px 15px rgba(0, 0, 0, 0.6),
    0px -1px 3px rgba(0, 0, 0, 0.8),
    inset 0px 1px 0px rgba(255, 255, 255, 0.1);
  border-top: 2px solid #444;
  overflow: hidden;
  z-index: 2;
}

#chapter-selector-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg,
    transparent 0%,
    var(--marvel-red) 20%,
    #ff6b6b 50%,
    var(--marvel-red) 80%,
    transparent 100%);
  animation: energyFlow 4s ease-in-out infinite;
}

@keyframes energyFlow {
  0%, 100% { opacity: 0.3; transform: scaleX(0.8); }
  50% { opacity: 0.8; transform: scaleX(1.2); }
}

#chapter-selector-title {
  font-size: 16px;
  font-weight: bold;
  padding: 12px 0 8px 20px;
  white-space: nowrap;
  position: relative;
  z-index: 2;
  text-align: left;
  color: #DDD;
  text-transform: uppercase;
  letter-spacing: 2px;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
  font-family: "DejaVu Sans Mono", "DejaVuSansMono", monospace;
  background: linear-gradient(90deg, var(--marvel-red), #ff6b6b, var(--marvel-red));
  background-size: 200% 100%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: textShimmer 3s ease-in-out infinite;
}

@keyframes textShimmer {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

#chapter-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 8px 20px 12px 20px;
  height: 50px;
  gap: 8px;
  flex-wrap: wrap;
  position: relative;
}

.chapter-btn {
  background: linear-gradient(145deg, #3a3a3a, #2a2a2a);
  color: #CCC;
  border: 2px solid #555;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 13px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 85px;
  height: 38px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-family: "DejaVu Sans Mono", "DejaVuSansMono", monospace;
  box-shadow:
    0 3px 10px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.3);
}

.chapter-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent);
  transition: left 0.4s ease;
}

.chapter-btn:hover {
  background: linear-gradient(145deg, #4a4a4a, #3a3a3a);
  color: #FFF;
  border-color: #777;
  transform: translateY(-2px);
  box-shadow:
    0 5px 15px rgba(0, 0, 0, 0.6),
    0 2px 8px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.25),
    inset 0 -1px 0 rgba(0, 0, 0, 0.3);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.chapter-btn:hover::before {
  left: 100%;
}

.chapter-btn.active {
  background: linear-gradient(145deg, var(--marvel-red), var(--marvel-red-dark));
  color: white;
  border-color: var(--marvel-red);
  box-shadow:
    0 0 15px rgba(236, 29, 36, 0.6),
    0 0 30px rgba(236, 29, 36, 0.3),
    0 5px 15px rgba(0, 0, 0, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.3);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
  transform: translateY(-2px);
  animation: marvelGlow 2.5s ease-in-out infinite alternate;
}

@keyframes marvelGlow {
  0% {
    box-shadow:
      0 0 15px rgba(236, 29, 36, 0.6),
      0 0 30px rgba(236, 29, 36, 0.3),
      0 5px 15px rgba(0, 0, 0, 0.5),
      inset 0 1px 0 rgba(255, 255, 255, 0.3),
      inset 0 -1px 0 rgba(0, 0, 0, 0.3);
  }
  100% {
    box-shadow:
      0 0 20px rgba(236, 29, 36, 0.8),
      0 0 40px rgba(236, 29, 36, 0.4),
      0 5px 15px rgba(0, 0, 0, 0.5),
      inset 0 1px 0 rgba(255, 255, 255, 0.3),
      inset 0 -1px 0 rgba(0, 0, 0, 0.3);
  }
}

.chapter-btn:active {
  transform: translateY(-1px);
  transition: transform 0.1s ease;
}

#chapter-info {
  font-size: 12px;
  font-weight: bold;
  color: #BBB;
  padding: 8px 20px 12px 20px;
  text-align: left;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-family: "DejaVu Sans Mono", "DejaVuSansMono", monospace;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
  position: relative;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.05) 0%, transparent 100%);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

#chapter-info::before {
  content: '▶ ';
  color: var(--marvel-red);
  margin-right: 6px;
  text-shadow: 0 0 5px rgba(236, 29, 36, 0.5);
}


/* -------- Extra sidebar with comics ------------*/

/* Closed comics bar */

#view-comics-container {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 298px;
  height: 75px;
  float: left;
  background-color: #2B2B2B;
  color: #AAA;
  text-align: center;
}
#view-comics {
  margin-top: 20px!important;
}
#view-comics > span {
  padding: 7px 2px 7px 10px;
}
#view-comics img {
  transform: rotate(90deg);
  position: relative;
  top: 6px;
  margin-right: 5px;
}

/* Comics Bar */

#comics-bar {
  position: absolute;
  bottom: 0;
  width: 297px;
  height: 100%;
  margin: 0;
  overflow: hidden;
  font-size: 14px;
  background-color: #222;
  color: #AAA;
  float: left;
  text-align: center;
  box-shadow: -2px 0 5px #000;
  transform: scaleY(0);
  transform-origin: bottom;
  -webkit-transition: 0.3s ease-in-out;
  -moz-transition: 0.3s ease-in-out;
  -o-transition: 0.3s ease-in-out;
  transition: 0.3s ease-in-out;
  z-index: 2;
}

#comics, #comic-details {
  padding: 5px;
  text-align: center;
  overflow-y: auto;
  line-height: 20px;
}
#comics {
  border-top: 1px dashed #444;
  overflow-x: hidden;
  min-height: 35%;
}
#comic-details {
  height: 50%;
  border-top: 1px dashed #444;
}

/* Actions Buttons */

#comics-actions {
  width: 100%;
  height: 47px;
  text-align: center;
  display: inline-flex;
  justify-content: center;
  margin: 0px;
}
#comics-actions button {
  margin-top: 4px;
  margin-bottom: 4px;
}

button.sort-button.selected {
  cursor: default;
}
button.sort-button img {
  width: 26px;
  height: 26px;
  margin: 3px 0px 0px 1px;
}

#comics-pause {
  display: none;
}

#filter-comics {
  display: none;
}
#filter-input {
  width: calc(100% - 7px);
  margin: -1px 0px 4px 0px;
  padding: 0 10px;
  font-family: "DejaVu Sans Mono", "DejaVuSansMono", monospace;
  font-size: 14px;
  color: #FFF;
  background: #333;
  border: 1px solid #444;
  line-height: 24px;
  outline: none;
}
#comics-subtitle {
  margin-bottom: 5px;
  display: none;
}

/* Comics List */

#comics-list {
  display: contents;
}
#comics-list li {
  padding: 5px;
  font-size: 15px;
  cursor: pointer;
  list-style-type: none;
}
#comics-list li:nth-child(even) {
  background: #2F2F2F;
}
@media (hover: hover) and (pointer: fine) {
  #comics-list li:hover {
    background: var(--marvel-red-dark);
    color: white;
  }
}
#comics-list li.selected {
  background: var(--marvel-red);
  color: white!important;
  font-weight: bold;
}
#comics-cache {
  display: none;
  opacity: 0;
  position: absolute;
  z-index: 5;
  cursor: pointer;
}

/* Single Comic Details */

#comic-img {
  max-width: 90%;
  padding-top: 5px;
}
#comic-title {
  margin: 0;
}
#comic-desc {
  line-height: 16px;
  padding-left: 10px;
  padding-right: 10px;
}
#comic-url {
  display: none;
}

.comic-entities {
  display: none;
  width: calc(50% - 4px);
  padding: 0;
  margin: 0;
  float: left;
}
.comic-entities h4 {
  margin: 10px 0 5px 0;
}
.comic-entities ul {
  padding: 0;
  margin: 0px 0px 10px 0px;
}
.comic-entities li {
  list-style-type: none;
  padding: 0;
  margin: 0;
}
.comic-entities li.entity-link {
  cursor: pointer;
}
#comic-creators {
  padding: 0 2px 0 0;
}
#comic-characters {
  padding: 0 0 0 2px;
}
#comic-creators li:nth-child(even) {
  background: #2F2F2F;
}
#comic-characters li:nth-child(odd) {
  background: #2F2F2F;
}


/* -------- Graph zone ------------*/

.sigma-container {
  position: absolute;
  top: 0px;
  left: 501px;
  height: calc(100% - 121px);
  width: calc(100% - 502px);
  margin: 0;
  padding: 0;
  overflow: hidden;
  background: radial-gradient(ellipse at center, #1a1a1a 0%, #000000 100%);
  background-attachment: fixed;
  /* Add subtle texture for depth */
  background-image:
    radial-gradient(circle at 25% 25%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(139, 0, 0, 0.1) 0%, transparent 50%);
}

.sigma-edges {
  opacity: 0.5;
}

/* Creators era labels layer */

#clusters-layer {
  width: 100%;
  height: 100%;
  position: absolute;
}
.cluster-label {
  position: absolute;
  transform: translate(-50%, -50%);
  text-transform: uppercase;
  font-weight: bold;
  font-size: 16px;
  padding: 3px 8px;
  background-color: rgba(0, 0, 0, 0.55);
  border-radius: 14px;
  text-align: center;
  /* Add colorful background glow like Marvel */
  box-shadow:
    0 0 30px currentColor,
    0 0 60px rgba(255, 255, 255, 0.1),
    0 0 100px currentColor;
  backdrop-filter: blur(2px);
}

/* Add background cluster areas */
.cluster-label::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, currentColor 0%, transparent 70%);
  opacity: 0.15;
  border-radius: 50%;
  z-index: -1;
  pointer-events: none;
}

/* Action Buttons */

#controls {
  position: absolute;
  top: 2px;
  text-align: center;
  width: 168px;
}
#sigma-buttons {
  display: flex;
  vertical-align: middle;
}

/* Story Intelligence Section in Sidebar */
#story-intelligence-section {
  margin-top: 15px;
  text-align: center;
  padding: 0 20px;
}

/* Story Intelligence Button */
.story-intelligence-button {
  background: linear-gradient(135deg, #4ecdc4, #44a08d) !important;
  border: 2px solid #4ecdc4 !important;
  border-radius: 8px !important;
  padding: 10px 16px !important;
  margin: 0 !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: 8px !important;
  color: white !important;
  font-weight: bold !important;
  font-size: 13px !important;
  transition: all 0.3s ease !important;
  cursor: pointer !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  width: auto !important;
  min-width: 200px !important;
}

.story-intelligence-button:hover {
  background: linear-gradient(135deg, #44a08d, #4ecdc4) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(78, 205, 196, 0.5) !important;
}

.story-intelligence-button.active {
  background: linear-gradient(135deg, #ffd700, #ffed4e) !important;
  border-color: #ffd700 !important;
  color: #333 !important;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4) !important;
}

.story-intelligence-button .story-icon {
  font-size: 16px;
}

.story-intelligence-button .story-text {
  font-size: 12px;
  font-weight: 600;
}

/* Story Dashboard Container */
.story-dashboard-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  background: #000;
}

#zoom-reset img {
  margin: 2px;
}

/* Story Success Dashboard Styles */
.success-dashboard {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 100%);
  color: #ffffff;
  overflow-y: auto;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.success-header {
  padding: 30px;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  border-bottom: 3px solid #4ecdc4;
}

.success-title {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.success-title h1 {
  font-size: 2.5em;
  margin: 0;
  background: linear-gradient(45deg, #4ecdc4, #44a08d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.book-info h2 {
  font-size: 1.8em;
  margin: 10px 0;
  color: #ffffff;
}

.success-score-main {
  display: flex;
  align-items: center;
  gap: 20px;
}

.score-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 4px solid;
  position: relative;
}

.score-circle.score-excellent {
  border-color: #00ff88;
  background: radial-gradient(circle, rgba(0, 255, 136, 0.1) 0%, rgba(0, 255, 136, 0.05) 100%);
}

.score-circle.score-good {
  border-color: #4ecdc4;
  background: radial-gradient(circle, rgba(78, 205, 196, 0.1) 0%, rgba(78, 205, 196, 0.05) 100%);
}

.score-circle.score-average {
  border-color: #f9ca24;
  background: radial-gradient(circle, rgba(249, 202, 36, 0.1) 0%, rgba(249, 202, 36, 0.05) 100%);
}

.score-circle.score-poor {
  border-color: #ff6b6b;
  background: radial-gradient(circle, rgba(255, 107, 107, 0.1) 0%, rgba(255, 107, 107, 0.05) 100%);
}

.score-number {
  font-size: 2.5em;
  font-weight: bold;
  line-height: 1;
}

.score-label {
  font-size: 0.9em;
  opacity: 0.8;
  margin-top: 5px;
}

.score-interpretation {
  max-width: 300px;
  font-size: 1.1em;
  line-height: 1.4;
}

.key-indicators {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 20px;
}

.indicator-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.indicator-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(78, 205, 196, 0.3);
  border-color: #4ecdc4;
}

.indicator-card.score-excellent {
  border-color: #00ff88;
}

.indicator-card.score-good {
  border-color: #4ecdc4;
}

.indicator-card.score-average {
  border-color: #f9ca24;
}

.indicator-card.score-poor {
  border-color: #ff6b6b;
}

.indicator-icon {
  font-size: 2em;
  margin-bottom: 10px;
}

.indicator-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.indicator-name {
  font-weight: 600;
  font-size: 1em;
}

.indicator-score {
  font-weight: bold;
  font-size: 1.1em;
}

.indicator-bar {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.indicator-fill {
  height: 100%;
  background: linear-gradient(90deg, #4ecdc4, #44a08d);
  border-radius: 3px;
  transition: width 0.8s ease;
}

.success-tabs {
  display: flex;
  background: rgba(0, 0, 0, 0.3);
  border-bottom: 1px solid #333;
  overflow-x: auto;
}

.success-tab {
  background: transparent;
  border: none;
  color: #ccc;
  padding: 15px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
  border-bottom: 3px solid transparent;
}

.success-tab:hover {
  background: rgba(78, 205, 196, 0.1);
  color: #4ecdc4;
}

.success-tab.active {
  background: rgba(78, 205, 196, 0.2);
  color: #4ecdc4;
  border-bottom-color: #4ecdc4;
}

.tab-icon {
  font-size: 1.2em;
}

.tab-label {
  font-weight: 500;
}

.success-content {
  padding: 30px;
  min-height: calc(100vh - 400px);
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #333;
}

.content-header h2 {
  font-size: 2em;
  margin: 0;
  background: linear-gradient(45deg, #4ecdc4, #44a08d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.score-badge {
  padding: 10px 20px;
  border-radius: 25px;
  font-weight: bold;
  font-size: 1.2em;
  border: 2px solid;
}

.score-badge.score-excellent {
  background: rgba(0, 255, 136, 0.1);
  border-color: #00ff88;
  color: #00ff88;
}

.score-badge.score-good {
  background: rgba(78, 205, 196, 0.1);
  border-color: #4ecdc4;
  color: #4ecdc4;
}

.score-badge.score-average {
  background: rgba(249, 202, 36, 0.1);
  border-color: #f9ca24;
  color: #f9ca24;
}

.score-badge.score-poor {
  background: rgba(255, 107, 107, 0.1);
  border-color: #ff6b6b;
  color: #ff6b6b;
}

/* Overview Content Styles */
.overview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.success-breakdown,
.market-prediction,
.improvement-recommendations,
.success-patterns {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 15px;
  padding: 25px;
  border: 1px solid rgba(78, 205, 196, 0.2);
}

.success-breakdown h3,
.market-prediction h3,
.improvement-recommendations h3,
.success-patterns h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #4ecdc4;
  font-size: 1.3em;
}

.breakdown-chart {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.breakdown-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.factor-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.factor-name {
  font-weight: 500;
  color: #ffffff;
}

.factor-score {
  font-weight: bold;
  color: #4ecdc4;
}

.factor-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.factor-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.8s ease;
}

.prediction-item {
  margin-bottom: 15px;
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.prediction-label {
  font-weight: 600;
  color: #4ecdc4;
  margin-bottom: 5px;
}

.prediction-value {
  color: #ffffff;
  line-height: 1.4;
}

.recommendation-item {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
  border-left: 4px solid;
}

.recommendation-item.priority-high {
  border-left-color: #ff6b6b;
}

.recommendation-item.priority-medium {
  border-left-color: #f9ca24;
}

.recommendation-item.priority-low {
  border-left-color: #4ecdc4;
}

.rec-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.rec-area {
  font-weight: bold;
  color: #4ecdc4;
}

.rec-priority {
  font-size: 0.9em;
  padding: 3px 8px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
}

.rec-issue {
  color: #ccc;
  margin-bottom: 8px;
  font-style: italic;
}

.rec-solution {
  color: #ffffff;
  font-weight: 500;
}

.pattern-item {
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.pattern-item:last-child {
  border-bottom: none;
}

/* Content Grid Styles */
.power-grid,
.emotional-grid,
.character-grid,
.secret-grid,
.relationship-grid,
.conflict-grid,
.world-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

/* Growth Events Details Styling */
.growth-events-details,
.emotional-peaks-details,
.character-details,
.secret-details,
.relationship-details,
.conflict-details {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 15px;
  padding: 25px;
  border: 1px solid rgba(78, 205, 196, 0.2);
}

.growth-events-list,
.peaks-list,
.character-breakdown-list,
.secret-breakdown-list,
.relationship-breakdown-list,
.conflict-breakdown-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  max-height: 400px;
  overflow-y: auto;
}

/* Growth Event Cards */
.growth-event-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
  border-left: 4px solid #4ecdc4;
  transition: all 0.3s ease;
}

.growth-event-card:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(5px);
  box-shadow: 0 4px 15px rgba(78, 205, 196, 0.2);
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.event-number {
  background: linear-gradient(45deg, #4ecdc4, #44a08d);
  color: #000;
  padding: 5px 12px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 0.9em;
}

.event-chapter {
  background: rgba(255, 255, 255, 0.1);
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.9em;
  color: #ccc;
}

.event-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.event-description {
  color: #ffffff;
  font-size: 1em;
  line-height: 1.4;
}

.event-impact {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.impact-label {
  color: #4ecdc4;
  font-weight: bold;
  font-size: 0.9em;
}

.impact-text {
  color: #ccc;
  font-size: 0.95em;
  line-height: 1.3;
}

/* Emotional Peaks Cards */
.peak-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
  border-left: 4px solid #ff6b6b;
  transition: all 0.3s ease;
}

.peak-card:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(5px);
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.2);
}

.peak-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.peak-rank {
  background: linear-gradient(45deg, #ff6b6b, #ff4757);
  color: #fff;
  padding: 5px 12px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 0.9em;
}

.peak-intensity {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
  padding: 5px 12px;
  border-radius: 15px;
  font-weight: bold;
  font-size: 0.9em;
}

.peak-chapter {
  background: rgba(255, 255, 255, 0.1);
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.9em;
  color: #ccc;
}

.peak-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.peak-type {
  color: #ff6b6b;
  font-weight: bold;
  font-size: 0.9em;
  text-transform: uppercase;
}

.peak-description {
  color: #ffffff;
  font-size: 1em;
  line-height: 1.4;
}

.peak-significance {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.significance-label {
  color: #ccc;
  font-size: 0.9em;
}

.significance-value {
  color: #f9ca24;
  font-weight: bold;
}

/* Character Breakdown Cards */
.character-breakdown-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
  border-left: 4px solid #f9ca24;
  transition: all 0.3s ease;
}

.character-breakdown-card:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(5px);
  box-shadow: 0 4px 15px rgba(249, 202, 36, 0.2);
}

.character-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.character-rank {
  background: linear-gradient(45deg, #f9ca24, #f39c12);
  color: #000;
  padding: 5px 12px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 0.9em;
}

.character-name {
  color: #ffffff;
  font-weight: bold;
  font-size: 1.1em;
  flex-grow: 1;
  text-align: center;
}

.character-importance {
  background: rgba(249, 202, 36, 0.2);
  color: #f9ca24;
  padding: 5px 12px;
  border-radius: 15px;
  font-weight: bold;
  font-size: 0.9em;
}

.character-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.character-archetype {
  color: #4ecdc4;
  font-weight: bold;
  font-size: 0.9em;
  text-transform: capitalize;
}

.character-presence {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.presence-label {
  color: #ccc;
  font-size: 0.9em;
}

.presence-value {
  color: #ffffff;
  font-weight: bold;
}

.character-motivations {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.motivations-label {
  color: #ccc;
  font-size: 0.9em;
}

.motivations-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.motivation-item {
  background: rgba(78, 205, 196, 0.2);
  color: #4ecdc4;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 0.8em;
}

/* Secret Breakdown Cards */
.secret-breakdown-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
  border-left: 4px solid #9b59b6;
  transition: all 0.3s ease;
}

.secret-breakdown-card:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(5px);
  box-shadow: 0 4px 15px rgba(155, 89, 182, 0.2);
}

.secret-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.secret-rank {
  background: linear-gradient(45deg, #9b59b6, #8e44ad);
  color: #fff;
  padding: 5px 12px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 0.9em;
}

.secret-characters {
  color: #ffffff;
  font-weight: bold;
  font-size: 1em;
  flex-grow: 1;
  text-align: center;
}

.secret-strength {
  background: rgba(155, 89, 182, 0.2);
  color: #9b59b6;
  padding: 5px 12px;
  border-radius: 15px;
  font-weight: bold;
  font-size: 0.9em;
}

.secret-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.secret-type {
  color: #9b59b6;
  font-weight: bold;
  font-size: 0.9em;
  text-transform: capitalize;
}

.secret-description {
  color: #ffffff;
  font-size: 1em;
  line-height: 1.4;
}

.secret-implications {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.implications-label {
  color: #ccc;
  font-size: 0.9em;
}

.implications-text {
  color: #9b59b6;
  font-size: 0.95em;
  line-height: 1.3;
}

/* Relationship Breakdown Cards */
.relationship-breakdown-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
  border-left: 4px solid #e74c3c;
  transition: all 0.3s ease;
}

.relationship-breakdown-card:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(5px);
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.2);
}

.relationship-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.relationship-rank {
  background: linear-gradient(45deg, #e74c3c, #c0392b);
  color: #fff;
  padding: 5px 12px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 0.9em;
}

.relationship-characters {
  color: #ffffff;
  font-weight: bold;
  font-size: 1em;
  flex-grow: 1;
  text-align: center;
}

.relationship-volatility {
  background: rgba(231, 76, 60, 0.2);
  color: #e74c3c;
  padding: 5px 12px;
  border-radius: 15px;
  font-weight: bold;
  font-size: 0.9em;
}

.relationship-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.relationship-type {
  color: #e74c3c;
  font-weight: bold;
  font-size: 0.9em;
  text-transform: capitalize;
}

.relationship-description {
  color: #ffffff;
  font-size: 1em;
  line-height: 1.4;
}

.relationship-stability,
.relationship-changes {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stability-label,
.changes-label {
  color: #ccc;
  font-size: 0.9em;
}

.stability-value {
  font-weight: bold;
  text-transform: capitalize;
}

.stability-value.volatile {
  color: #e74c3c;
}

.stability-value.unstable {
  color: #f39c12;
}

.stability-value.stable {
  color: #27ae60;
}

.changes-value {
  color: #ffffff;
  font-weight: bold;
}

/* Conflict Breakdown Cards */
.conflict-breakdown-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
  border-left: 4px solid #e67e22;
  transition: all 0.3s ease;
}

.conflict-breakdown-card:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(5px);
  box-shadow: 0 4px 15px rgba(230, 126, 34, 0.2);
}

.conflict-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.conflict-rank {
  background: linear-gradient(45deg, #e67e22, #d35400);
  color: #fff;
  padding: 5px 12px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 0.9em;
}

.conflict-characters {
  color: #ffffff;
  font-weight: bold;
  font-size: 1em;
  flex-grow: 1;
  text-align: center;
}

.conflict-intensity {
  background: rgba(230, 126, 34, 0.2);
  color: #e67e22;
  padding: 5px 12px;
  border-radius: 15px;
  font-weight: bold;
  font-size: 0.9em;
}

.conflict-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.conflict-type {
  color: #e67e22;
  font-weight: bold;
  font-size: 0.9em;
  text-transform: capitalize;
}

.conflict-description {
  color: #ffffff;
  font-size: 1em;
  line-height: 1.4;
}

.conflict-interactions,
.conflict-influence {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.interactions-label,
.influence-label {
  color: #ccc;
  font-size: 0.9em;
}

.interactions-value {
  color: #ffffff;
  font-weight: bold;
}

.influence-text {
  color: #e67e22;
  font-size: 0.95em;
  line-height: 1.3;
  max-width: 60%;
  text-align: right;
}

/* No Data Styling */
.no-data {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 40px 20px;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 10px;
  border: 1px dashed rgba(255, 255, 255, 0.1);
}

/* Story Overview Panel Styles */

.overview-section {
  background: rgba(255, 255, 255, 0.04);
  border-radius: 16px;
  padding: 28px;
  border: 1px solid rgba(78, 205, 196, 0.25);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  height: fit-content;
  min-height: 200px;
  flex: 1 1 calc(50% - 12px);
  box-sizing: border-box;
}

.overview-section:hover {
  background: rgba(255, 255, 255, 0.06);
  border-color: rgba(78, 205, 196, 0.35);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.overview-section h3 {
  margin-top: 0;
  margin-bottom: 22px;
  color: #4ecdc4;
  font-size: 1.35em;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.theme-list,
.plot-arc-list,
.pattern-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Enhanced Pattern List Styling */
.pattern-list {
  gap: 8px;
  max-height: none;
  overflow: visible;
}

.relationship-patterns .pattern-list {
  gap: 10px;
}

.theme-item,
.plot-arc-item,
.pattern-item {
  background: rgba(255, 255, 255, 0.05);
  padding: 15px 18px;
  border-radius: 10px;
  color: #ffffff;
  border-left: 4px solid #4ecdc4;
  transition: all 0.3s ease;
  line-height: 1.6;
  font-size: 0.95em;
  margin-bottom: 12px;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.theme-item:hover,
.plot-arc-item:hover,
.pattern-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(3px);
  box-shadow: 0 2px 8px rgba(78, 205, 196, 0.15);
}

/* Enhanced Relationship Patterns Styling */
.relationship-patterns .pattern-item {
  background: rgba(255, 255, 255, 0.06);
  border-left: 4px solid #e67e22;
  padding: 18px 20px;
  margin-bottom: 15px;
  border-radius: 12px;
  font-size: 0.98em;
  line-height: 1.7;
  color: #f8f9fa;
  text-align: left;
  white-space: normal;
  word-break: break-word;
  hyphens: auto;
}

.relationship-patterns .pattern-item:hover {
  background: rgba(255, 255, 255, 0.09);
  border-left-color: #f39c12;
  transform: translateX(5px);
  box-shadow: 0 4px 12px rgba(230, 126, 34, 0.2);
}

.relationship-patterns h3 {
  color: #e67e22;
  margin-bottom: 25px;
  font-size: 1.35em;
  font-weight: 600;
}

/* Improved text readability for all pattern items */
.pattern-item p,
.pattern-item span,
.pattern-item div {
  margin: 0;
  padding: 0;
  line-height: inherit;
  word-wrap: break-word;
}

/* Ensure proper text flow in overview sections */
.overview-content .content-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

@media (max-width: 1200px) {
  .overview-content .content-grid {
    flex-direction: column;
    gap: 20px;
  }

  .overview-section {
    flex: 1 1 100%;
  }

  .pattern-item,
  .theme-item,
  .plot-arc-item {
    font-size: 0.92em;
    padding: 16px 18px;
  }
}

.character-ranking {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.ranking-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.05);
  padding: 12px 15px;
  border-radius: 8px;
  border-left: 3px solid #f9ca24;
  transition: all 0.3s ease;
  cursor: pointer;
}

.ranking-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(3px);
}

.character-name {
  color: #ffffff;
  font-weight: bold;
}

.influence-score {
  color: #f9ca24;
  font-weight: bold;
  background: rgba(249, 202, 36, 0.2);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.9em;
}

.progression-stats,
.intensity-stats,
.archetype-distribution,
.secret-stats,
.volatility-stats,
.conflict-stats,
.world-stats {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 15px;
  padding: 25px;
  border: 1px solid rgba(78, 205, 196, 0.2);
}

.progression-analysis,
.intensity-analysis,
.character-stats,
.revelation-timeline,
.intensity-trends {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 15px;
  padding: 25px;
  border: 1px solid rgba(78, 205, 196, 0.2);
}

.progression-timeline {
  grid-column: 1 / -1;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 15px;
  padding: 25px;
  border: 1px solid rgba(78, 205, 196, 0.2);
}

.stat-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.stat-item {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 15px;
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 0.9em;
  color: #ccc;
  margin-bottom: 8px;
}

.stat-value {
  display: block;
  font-size: 1.5em;
  font-weight: bold;
  color: #4ecdc4;
}

.analysis-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.analysis-item {
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
}

.analysis-text {
  color: #ffffff;
  line-height: 1.5;
  margin-bottom: 10px;
}

.analysis-label {
  font-weight: bold;
  color: #4ecdc4;
  margin-bottom: 5px;
}

.analysis-detail {
  color: #ccc;
  font-size: 0.95em;
  margin-bottom: 3px;
}

.evolution-summary {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 20px;
}

.evolution-state h4 {
  color: #4ecdc4;
  margin-bottom: 10px;
}

.evolution-state p {
  color: #ffffff;
  line-height: 1.5;
  margin: 0;
}

.trend-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.trend-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.trend-label {
  color: #ccc;
}

.trend-value {
  font-weight: bold;
  padding: 5px 10px;
  border-radius: 15px;
}

.trend-value.escalating {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
}

.trend-value.stable {
  background: rgba(78, 205, 196, 0.2);
  color: #4ecdc4;
}

.trend-value.declining {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
}

.trend-value.optimal {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
}

.trend-value.suboptimal {
  background: rgba(249, 202, 36, 0.2);
  color: #f9ca24;
}

/* Archetype Chart Styles */
.distribution-chart {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.archetype-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.archetype-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.archetype-name {
  font-weight: 500;
  color: #ffffff;
  text-transform: capitalize;
}

.archetype-count {
  font-weight: bold;
  color: #4ecdc4;
}

.archetype-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.archetype-fill {
  height: 100%;
  background: linear-gradient(90deg, #4ecdc4, #44a08d);
  border-radius: 4px;
  transition: width 0.8s ease;
}

.timeline-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.timeline-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.timeline-label {
  color: #ccc;
}

.timeline-value {
  font-weight: bold;
  color: #4ecdc4;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 1.2em;
  color: #4ecdc4;
}

.error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 1.2em;
  color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 10px;
  margin: 20px;
}

/* Enhanced Interactive Indicator Cards */
.interactive-card {
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.interactive-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(78, 205, 196, 0.1), transparent);
  transition: left 0.6s ease;
}

.interactive-card:hover::before {
  left: 100%;
}

.interactive-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 15px 40px rgba(78, 205, 196, 0.4);
  border-color: #4ecdc4;
}

.indicator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.indicator-badge {
  padding: 5px 12px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 0.9em;
  border: 2px solid;
}

.indicator-subtitle {
  font-size: 0.85em;
  color: #ccc;
  margin-bottom: 8px;
  line-height: 1.3;
}

.indicator-highlight {
  font-size: 0.8em;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  background: rgba(78, 205, 196, 0.1);
  color: #4ecdc4;
  margin-bottom: 10px;
  display: inline-block;
}

.indicator-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
  padding-top: 10px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.interactive-card:hover .indicator-action {
  opacity: 1;
}

.action-text {
  font-size: 0.8em;
  color: #4ecdc4;
}

.action-arrow {
  font-size: 1.2em;
  color: #4ecdc4;
  transform: translateX(0);
  transition: transform 0.3s ease;
}

.interactive-card:hover .action-arrow {
  transform: translateX(5px);
}

/* World Building Comprehensive Grid */
.world-comprehensive-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  grid-template-areas:
    "overview settings"
    "themes plots"
    "immersion progression";
}

.world-overview-section {
  grid-area: overview;
}

.settings-analysis-section {
  grid-area: settings;
}

.themes-analysis-section {
  grid-area: themes;
}

.plot-arcs-section {
  grid-area: plots;
}

.immersion-factors-section {
  grid-area: immersion;
}

.narrative-progression-section {
  grid-area: progression;
}

.world-overview-section,
.settings-analysis-section,
.themes-analysis-section,
.plot-arcs-section,
.immersion-factors-section,
.narrative-progression-section {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 15px;
  padding: 25px;
  border: 1px solid rgba(78, 205, 196, 0.2);
}

/* Success Factors Grid */
.success-factors-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.success-factor-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.success-factor-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(78, 205, 196, 0.2);
}

.factor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.factor-icon {
  font-size: 1.8em;
}

.factor-score {
  font-weight: bold;
  font-size: 1.1em;
  color: #4ecdc4;
}

.factor-content h4 {
  margin: 0 0 8px 0;
  color: #ffffff;
  font-size: 1.1em;
}

.factor-content p {
  margin: 0;
  color: #ccc;
  font-size: 0.9em;
  line-height: 1.4;
}

.factor-bar {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-top: 15px;
}

.factor-fill {
  height: 100%;
  background: linear-gradient(90deg, #4ecdc4, #44a08d);
  border-radius: 3px;
  transition: width 0.8s ease;
}

/* Settings Analysis Styles */
.settings-overview {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.settings-summary {
  display: flex;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 15px;
}

.summary-stat {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 0.8em;
  color: #ccc;
  margin-bottom: 5px;
}

.stat-value {
  display: block;
  font-size: 1.2em;
  font-weight: bold;
  color: #4ecdc4;
}

.categories-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.category-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 15px;
  border-left: 4px solid #4ecdc4;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.category-header h5 {
  margin: 0;
  color: #4ecdc4;
  font-size: 1em;
}

.category-count {
  background: rgba(78, 205, 196, 0.2);
  color: #4ecdc4;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.8em;
  font-weight: bold;
}

.category-settings {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.setting-tag {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 0.8em;
}

.settings-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.setting-detail-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 15px;
  border-left: 3px solid #4ecdc4;
}

.setting-name {
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 10px;
  font-size: 1em;
}

.setting-metrics {
  display: flex;
  gap: 20px;
}

.setting-metric {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.metric-label {
  font-size: 0.8em;
  color: #ccc;
}

.metric-value {
  font-size: 0.9em;
  font-weight: bold;
  color: #4ecdc4;
}

/* Themes Analysis Styles */
.themes-overview {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.themes-summary {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 15px;
}

.theme-categories-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.theme-category-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 15px;
  border-left: 4px solid #f9ca24;
}

.theme-category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.theme-category-header h5 {
  margin: 0;
  color: #f9ca24;
  font-size: 1em;
}

.theme-category-count {
  background: rgba(249, 202, 36, 0.2);
  color: #f9ca24;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.8em;
  font-weight: bold;
}

.theme-category-items {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.theme-tag {
  background: rgba(249, 202, 36, 0.1);
  color: #f9ca24;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 0.8em;
  border: 1px solid rgba(249, 202, 36, 0.3);
}

.themes-performance-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.theme-performance-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 15px;
  border-left: 3px solid #f9ca24;
}

.theme-name {
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 12px;
  font-size: 1em;
}

.theme-performance-metrics {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.performance-metric {
  display: flex;
  align-items: center;
  gap: 15px;
}

.performance-metric .metric-label {
  min-width: 100px;
  font-size: 0.8em;
  color: #ccc;
}

.metric-bar {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  background: linear-gradient(90deg, #f9ca24, #f0932b);
  border-radius: 4px;
  transition: width 0.8s ease;
}

.metric-score {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.7em;
  font-weight: bold;
  color: #ffffff;
}

/* Plot Arcs Analysis Styles */
.plot-arcs-overview {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.plot-summary {
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 15px;
}

.plot-arcs-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.plot-arc-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
  border-left: 4px solid #6c5ce7;
  transition: all 0.3s ease;
}

.plot-arc-card:hover {
  transform: translateX(5px);
  box-shadow: 0 5px 15px rgba(108, 92, 231, 0.2);
}

.arc-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.arc-number {
  background: rgba(108, 92, 231, 0.2);
  color: #6c5ce7;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.8em;
  font-weight: bold;
}

.arc-type-badge {
  background: rgba(108, 92, 231, 0.1);
  color: #6c5ce7;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 0.75em;
  border: 1px solid rgba(108, 92, 231, 0.3);
}

.arc-description {
  color: #ffffff;
  line-height: 1.5;
  margin-bottom: 15px;
  font-size: 0.95em;
}

.arc-metrics {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.arc-metric {
  display: flex;
  align-items: center;
  gap: 15px;
}

.arc-metric .metric-label {
  min-width: 80px;
  font-size: 0.8em;
  color: #ccc;
}

.arc-characters {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 5px;
}

.character-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.character-tag {
  background: rgba(108, 92, 231, 0.2);
  color: #6c5ce7;
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 0.75em;
  font-weight: 500;
}

/* Immersion Analysis Styles */
.immersion-analysis-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.immersion-metrics {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.immersion-metric {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 15px;
}

.metric-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.metric-icon {
  font-size: 1.2em;
}

.metric-name {
  font-weight: 600;
  color: #ffffff;
}

.metric-score-bar {
  display: flex;
  align-items: center;
  gap: 10px;
}

.metric-score-bar .metric-fill {
  flex: 1;
  height: 10px;
  background: linear-gradient(90deg, #4ecdc4, #44a08d);
  border-radius: 5px;
}

.metric-score-bar .metric-value {
  font-weight: bold;
  color: #4ecdc4;
  font-size: 0.9em;
}

.overall-immersion {
  text-align: center;
  margin-top: 20px;
}

.overall-score {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border-radius: 15px;
  border: 3px solid;
  background: rgba(255, 255, 255, 0.05);
}

.score-label {
  font-size: 0.9em;
  margin-bottom: 8px;
  opacity: 0.8;
}

.score-value {
  font-size: 2em;
  font-weight: bold;
}

/* Franchise Potential Styles */
.franchise-potential {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(255, 215, 0, 0.2);
}

.franchise-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 20px;
}

.franchise-metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.franchise-label {
  color: #ccc;
  font-size: 0.9em;
}

.franchise-value {
  color: #ffd700;
  font-weight: bold;
}

.franchise-verdict {
  text-align: center;
}

.verdict-score {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  padding: 15px 25px;
  border-radius: 12px;
  border: 2px solid;
  background: rgba(255, 255, 255, 0.05);
  margin-bottom: 15px;
}

.verdict-label {
  font-size: 0.9em;
  margin-bottom: 5px;
  opacity: 0.8;
}

.verdict-value {
  font-size: 1.5em;
  font-weight: bold;
  margin-bottom: 5px;
}

.verdict-score-value {
  font-size: 1em;
  opacity: 0.8;
}

.verdict-explanation {
  color: #ccc;
  line-height: 1.5;
  font-size: 0.95em;
}

/* Narrative Progression Styles */
.narrative-progression-content {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.progression-description p {
  color: #ffffff;
  line-height: 1.6;
  font-size: 1em;
  margin: 0;
  background: rgba(255, 255, 255, 0.05);
  padding: 15px;
  border-radius: 10px;
  border-left: 4px solid #4ecdc4;
}

.insights-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.insight-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.insight-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(78, 205, 196, 0.2);
  border-color: rgba(78, 205, 196, 0.3);
}

.insight-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.insight-icon {
  font-size: 1.5em;
}

.insight-title {
  font-weight: 600;
  color: #ffffff;
  font-size: 1em;
}

.insight-score {
  font-size: 1.8em;
  font-weight: bold;
  text-align: center;
  margin-bottom: 10px;
  padding: 10px;
  border-radius: 10px;
  border: 2px solid;
}

.insight-description {
  color: #ccc;
  font-size: 0.85em;
  line-height: 1.4;
  margin-bottom: 15px;
}

.insight-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.insight-fill {
  height: 100%;
  background: linear-gradient(90deg, #4ecdc4, #44a08d);
  border-radius: 4px;
  transition: width 0.8s ease;
}

.no-data {
  text-align: center;
  color: #ccc;
  font-style: italic;
  padding: 20px;
}

#regscreen {
  display: none;
}
#fullscreen {
  display: block;
}
@media all and (display-mode: fullscreen) {
  #regscreen {
    display: block;
  }
  #fullscreen {
    display: none;
  }
}

#search {
  margin: 2px;
  margin-right: 5px;
  width: 164px;
}
#search-icon {
  position: absolute;
  top: 49px;
  width: 18px;
  height: 18px;
  cursor: pointer;
}
#search input {
  width: 100%;
  padding: 4px 5px 4px 27px;
  text-align: center;
  font-family: "DejaVu Sans Mono", "DejaVuSansMono", monospace;
  font-size: 14px;
  line-height: 19px;
  color: #FFF;
  background: #333;
  display: block;
  border: 1px solid #444;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  outline: none;
}
#suggestions-select {
  display: none;
  font-family: "DejaVu Sans Mono", "DejaVuSansMono", monospace;
  width: 164px;
  height: 28px;
  border: 1px solid #444;
  padding-left: 20px;
  color: #FFF;
  background: #333;
}
/* Handle Chrome's blue cross and focus border */
input::-webkit-search-cancel-button {
  filter: grayscale(100%);
  cursor: pointer;
}
#suggestions-select:focus {
  outline-style: none;
  box-shadow: none;
}


/* Hint legend box */

#legend {
  position: absolute;
  opacity: 0.15;
  top: 4px;
  line-height: 19px;
  font-size: 14px;
  padding: 5px 10px 8px 10px;
  background-color: #444;
  border-radius: 1px;
  text-align: center;
  color: white;
  -webkit-transition: opacity 0.25s ease-in-out;
  -moz-transition: opacity 0.25s ease-in-out;
  -o-transition: opacity 0.25s ease-in-out;
  transition: opacity 0.25s ease-in-out;
}
#legend p {
  margin: 0;
}


/* -------- Loaders ------------*/

.loader {
  position: absolute;
  z-index: 5;
  display: none;
  opacity: 0.85;
  -webkit-transition: opacity 0.25s ease-in-out;
  -moz-transition: opacity 0.25s ease-in-out;
  -o-transition: opacity 0.25s ease-in-out;
  transition: opacity 0.25s ease-in-out;
}
.loader img {
  width: 45px;
  filter: invert(0.1);
}

#loader {
  top: calc(50% - 60px);
  left: calc(50% + 125px);
  opacity: 0;
}

#loader-comics {
  bottom: 20px;
  left: calc(50% - 68px);
  display: flex;
  align-items: center;
  opacity: 0.6;
}
#loader-comics img {
  width: 42px;
}
#loader-comics span {
  font-size: 12px;
  font-weight: bold;
  white-space: nowrap;
  color: #888;
  margin-left: 10px;
}

#loader-list {
  display: none;
}
#loader-list img {
  position: relative;
  top: 35px;
  left: 0;
  width: 45px;
}


/* -------- Modals ------------*/

.modal {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 20;
  display: none;
  background-color: rgba(0, 0, 0, 0.85);
  text-align: center;
  cursor: pointer;
}

/* FullScreen images modal */

#modal-helper {
  height: 100%;
  display: inline-block;
  vertical-align: middle;
}
#modal-img {
  max-width: calc(100% - 15px);
  max-height: calc(100% - 55px);
  z-index: 20;
  vertical-align: middle;
  margin: 5px 5px 50px 0;
}
#modal-img-missing {
  display: none;
  position: absolute;
  top: 45%;
  left: calc(50% - 150px);
  color: var(--marvel-red-light);
  font-size: 36px;
  font-weight: bold;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  padding: 5px 10px;
  z-index: 25;
}

#modal-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 50px;
}
#modal-actions button {
  margin: 5px 10px;
  display: inline-block;
}
#modal button img {
  z-index: -1;
}
#modal-pause {
  display: none;
}

/* Helpbox modal */

#help-box {
  position: absolute;
  top: 0;
  left: 15%;
  width: calc(70% - 60px);
  height: calc(100% - 60px);
  z-index: 25;
  overflow-y: auto;
  background-color: #444;
  color: #CCC;
  text-align: left;
  padding: 30px;
  margin: 0;
  transform: scale(0);
  -webkit-transition: transform .3s linear;
  -moz-transition: transform .3s linear;
  -o-transition: transform .3s linear;
  transition: transform .3s linear;
  cursor: default;
}
#help-box p {
  margin: 8px;
  font-size: 14px;
  line-height: 24px;
}
#help-box a, #help-box a:visited {
  color: #af7f7f;
  text-decoration: none;
}
#help-box a:hover, #help-box a:visited:hover {
  text-decoration: underline;
}

/* Mobile Portrait modal */

#rotate-modal {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 15;
  background-color: rgba(0, 0, 0, 0.85);
  text-align: center;
}

#rotate-modal h2 {
  background-color: var(--marvel-red);
  color: white;
  font-size: 34px!important;
  padding: 5px 0!important;
  margin: 0px 0px 10px 0px;
}
#rotate-modal .marvel {
  font-size: 36px!important;
  transform: scaleY(1.2)!important;
  padding-right: 15px!important;
}

#rotate-modal-img {
  position: relative;
  top: 25%;
  left: 0;
  min-width: 25%;
  max-width: 45%;
}

#rotate-modal p {
  display: block;
  position: relative;
  top: 25%;
  left: 25%;
  width: 50%;
  background-color: var(--marvel-red);
  color: white;
  font-size: 20px;
  text-transform: uppercase;
  font-weight: bold;
  padding: 15px 0px;
}
#rotate-modal p small {
  font-size: 14px;
  text-transform: lowercase;
  padding: 15px 5px 5px 5px;
  display: inline-block;
  line-height: 20px;
}

@media (orientation: landscape) {
  #rotate-modal {
    z-index: -1;
    opacity: 0;
    -webkit-transition: opacity 0.5s linear;
    -moz-transition: opacity 0.5s linear;
    -o-transition: opacity 0.5s linear;
    transition: opacity 0.5s linear;
  }
  #rotate-modal p, #rotate-modal-img {
    height: 0;
  }
}

@media (orientation: portrait) {
  #rotate-modal {
    opacity: 1;
    -webkit-transition: 0.1s linear;
    -moz-transition: 0.1s linear;
    -o-transition: 0.1s linear;
    transition: 0.1s linear;
  }
  .logo {
    display: block;
    padding: 5px;
    height: 44px;
  }
  #sidebar, #controls, #comics-bar, #chapter-selector-container, #view-comics-container {
    display: none!important;
  }
  #explanations, #node-details {
    opacity: 0!important;
  }
  #loader {
    top: 20%;
    left: calc(50% - 25px)!important;
  }
  #sigma-container {
    left: 0;
    width: 100%;
    height: 100%;
  }
}

@media (orientation: portrait) and (min-height: 1000px) {
  #rotate-modal p {
    font-size: 24px;
  }
  #rotate-modal p small {
    display: none;
  }
}


/* -------- Responsiveness ------------*/

.show-middle {
  display: none;
}

@media (min-width: 700px) {
  #controls {
    left: 201px;
  }
  #search-icon {
    left: 7px;
  }
  #comics-bar {
    right: 0;
  }
}

@media (min-width: 1001px) {
  #controls {
    left: 302px;
  }
  #search-icon {
    left: 7px;
  }
  .large-bar {
    display: inline;
  }
}

@media (max-width: 1000px) {
  .hide-small {
    display: none;
  }
  .show-middle {
    display: inline;
  }
  #sidebar, #choices, #credits {
    width: 198px;
  }

  /* Adjust story intelligence button for smaller screens */
  #story-intelligence-section {
    padding: 0 10px;
  }

  .story-intelligence-button {
    min-width: 160px !important;
    font-size: 11px !important;
    padding: 8px 12px !important;
  }

  .story-intelligence-button .story-icon {
    font-size: 14px;
  }

  .story-intelligence-button .story-text {
    font-size: 10px;
  }
  .sigma-container {
    left: 199px;
    width: calc(100% - 200px);
  }
  #comics-bar {
    width: 254px;
  }
  #view-comics-container {
    width: 255px;
  }
  #chapter-selector-container {
    left: 200px;
    width: calc(100% - 453px);
    height: 100px;
  }

  .chapter-btn {
    min-width: 75px;
    font-size: 11px;
    padding: 6px 12px;
    height: 32px;
  }

  #chapter-buttons {
    height: 42px;
    margin: 6px 16px 8px 16px;
  }

  #chapter-selector-title {
    font-size: 14px;
    padding: 8px 0 6px 16px;
  }
  #comics-filter {
    display: none;
  }
  h5 {
    font-size: 14px;
    margin-bottom: 6px;
  }
  #explanations p, #node-extra {
    margin: 7px 8px;
  }
  #histogram-legend .hidable {
    display: none;
  }
  #legend {
    line-height: 19px;
    font-size: 13px;
  }
  #loader {
    left: calc(50% + 75px);
  }
  .logo {
    height: 42px;
  }
  h1 {
    font-size: 20px!important;
    padding: 10px 0!important;
  }
  .marvel {
    font-size: 22px!important;
    transform: scaleY(1.3);
    padding-right: 7px!important;
    letter-spacing: -0.5px!important;
  }
  h2 {
    font-size: 14.6px;
    padding: 0px 2px;
  }
  .network-switch-label {
    width: 190px;
  }
  #choices {
    padding: 6px 0 0 0;
    height: 56px;
  }
  .toggle {
    width: 93px;
    height: 30px;
  }
  .names > div {
    font-size: 14px;
  }
  #view-node, #view-comics {
    font-size: 14px;
  }
  #sidebar, #comics-bar {
    font-size: 13px;
  }
  #comics-list li {
    padding: 2px;
  }
  .large-bar {
    display: none;
  }
}

@media (max-width: 700px) {
  #choices {
    height: 100px!important;
    padding: 0!important;
  }
  #comics-bar {
    left: 0;
    bottom: 35px;
    height: calc(100% - 84px);
    border: 1px solid #333;
    border-radius: 0 6px 6px 0;
    box-shadow: 2px 2px 5px #000;
  }
  #controls {
    right: 2px;
  }
  #legend {
    transform: translateX(-168px);
  }
  #search-icon {
    right: 144px;
  }
  #chapter-selector-container {
    width: calc(100% - 198px);
    height: 90px;
  }

  /* Mobile story intelligence button adjustments */
  #story-intelligence-section {
    padding: 0 8px;
    margin-top: 10px;
  }

  .story-intelligence-button {
    min-width: 140px !important;
    font-size: 10px !important;
    padding: 6px 10px !important;
    gap: 4px !important;
  }

  .story-intelligence-button .story-icon {
    font-size: 12px;
  }

  .story-intelligence-button .story-text {
    font-size: 9px;
  }

  .chapter-btn {
    min-width: 65px;
    font-size: 10px;
    padding: 5px 10px;
    height: 28px;
  }

  #chapter-buttons {
    gap: 4px;
    margin: 4px 12px 6px 12px;
    height: 36px;
  }

  #chapter-selector-title {
    font-size: 12px;
    padding: 6px 0 4px 12px;
  }

  #chapter-info {
    font-size: 10px;
    padding: 4px 12px 8px 12px;
  }
  #view-comics-container {
    position: fixed;
    left: 0;
    width: 198px;
    height: 37px;
    bottom: 36px;
    background-color: #222;
    padding-bottom: 4px;
    z-index: 2;
  }
  #view-comics {
    border-top: 1px dashed #444;
    width: 100%;
    padding: 0;
    margin: 0!important;
  }
  #view-comics span {
    padding: 5px 10px;
    margin: 0
  }
  #view-comics img {
    margin: 0;
    top: 7px;
  }
  #loader-comics {
    left: calc(50% + 14px);
  }
  h5 {
    padding-top: 6px;
  }
  .hide-small {
    display: none;
  }
}

@media (max-width: 700px) and (min-height: 600px) {
  #choices {
    height: 104px!important;
  }
}
@media (max-height: 400px) {
  h2 {
    font-size: 14.6px;
    padding: 0px 2px;
  }
  #comic-details {
    height: calc(100% / 3);
  }
  #comics-list li {
    font-size: 13px!important;
  }
}

@media (max-height: 600px) {
  h2 {
    font-size: 14.6px;
    padding: 0px 2px;
  }
  #header {
    padding-bottom: 5px;
  }
  .network-choice {
    height: 25px;
    margin-bottom: 8px;
  }
  .toggle {
    height: 27px;
  }
  #choices {
    padding: 6px 0 0 0;
    height: 56px;
  }
  h5 {
    font-size: 14px;
    margin-bottom: 6px;
  }
  .logo {
    height: 42px;
  }
  h1 {
    font-size: 28px;
    padding: 4px 0;
    margin: 0 0 5px 0;
  }
  .marvel {
    font-size: 32px;
    transform: scaleY(1.3);
  }
  #sidebar, #comics-bar {
    font-size: 13px;
  }
  #comics {
    line-height: 18px;
  }
  #comics-list li {
    padding: 2px;
    font-size: 14px;
  }
  #loader-list img {
    top: 10px;
  }
  #modal-img {
    max-height: calc(100% - 15px);
    max-width: calc(100% - 15px);
    margin: 0;
  }
  #modal-actions {
    height: 100%;
  }
  #modal-actions button {
    position: absolute;
    margin: 5px;
  }
  #modal-previous {
    top: calc(50% - 17px);
    left: 0;
  }
  #modal-next {
    top: calc(50% - 17px);
    right: 0;
  }
  #modal-play, #modal-pause {
    bottom: calc(100% - 45px);
    left: 0;
  }
}


/* -------- Touch handling ------------*/

@media not (pointer: coarse) {
  .histobar-hover:hover {
    background-color: white;
    opacity: 0.25;
  }
  .desktop-details {
    display: inline;
  }
  .mobiles-details {
    display: none;
  }
}

@media (pointer: coarse) {
  html, body {
    touch-action: pan-x, pan-y;
  }
  * {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  .histobar-hover.highlighted {
    background-color: white;
    opacity: 0.25;
  }
  input, textarea, button, select, a, label, div, p {
    -webkit-tap-highlight-color: rgba(0,0,0,0);
  }
  #search input {
    display: none;
  }
  #suggestions-select {
    display: block;
  }
  #comics-cache {
    display: none!important;
  }
  .desktop-details {
    display: none;
  }
  .mobiles-details {
    display: inline;
  }
}
