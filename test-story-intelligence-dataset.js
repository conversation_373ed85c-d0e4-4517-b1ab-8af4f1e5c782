// Test script to verify Story Intelligence Service dataset behavior
// This simulates the behavior that was causing the issue

console.log("🧪 Testing Story Intelligence Service Dataset Behavior");
console.log("=====================================================");

// Simulate the story intelligence service behavior
class MockStoryIntelligenceService {
  constructor() {
    this.currentDataset = 'HarryPotter';
    this.loaded = false;
  }

  // OLD BEHAVIOR (causing the issue)
  async initializeOld(dataset = 'HarryPotter') {
    console.log(`📊 OLD: initialize(${dataset}) called`);
    console.log(`📊 OLD: currentDataset was: ${this.currentDataset}`);
    
    if (this.currentDataset !== dataset || !this.loaded) {
      this.currentDataset = dataset;
      console.log(`📊 OLD: currentDataset changed to: ${this.currentDataset}`);
    }
    
    this.loaded = true;
    return Promise.resolve();
  }

  // NEW BEHAVIOR (fixed)
  async initializeNew(dataset) {
    const targetDataset = dataset || this.currentDataset;
    console.log(`📊 NEW: initialize(${dataset}) called, using: ${targetDataset}`);
    console.log(`📊 NEW: currentDataset was: ${this.currentDataset}`);
    
    if (this.currentDataset !== targetDataset || !this.loaded) {
      this.currentDataset = targetDataset;
      console.log(`📊 NEW: currentDataset changed to: ${this.currentDataset}`);
    }
    
    this.loaded = true;
    return Promise.resolve();
  }

  setCurrentDataset(dataset) {
    console.log(`🔄 setCurrentDataset(${dataset}) called`);
    this.currentDataset = dataset;
  }

  getCurrentDataset() {
    return this.currentDataset;
  }

  reset() {
    this.currentDataset = 'HarryPotter';
    this.loaded = false;
  }
}

async function testOldBehavior() {
  console.log("\n❌ Testing OLD Behavior (causing the issue):");
  console.log("===========================================");
  
  const service = new MockStoryIntelligenceService();
  
  // 1. User switches to Vampire in main app
  console.log("1. User switches to Vampire in main app");
  service.setCurrentDataset('Vampire');
  await service.initializeOld('Vampire');
  console.log(`   Current dataset: ${service.getCurrentDataset()}`);
  
  // 2. User opens Story Explorer (calls initialize without parameter)
  console.log("\n2. User opens Story Explorer (calls initialize() without parameter)");
  await service.initializeOld(); // This defaults to 'HarryPotter'!
  console.log(`   Current dataset: ${service.getCurrentDataset()}`);
  console.log("   ❌ PROBLEM: Dataset switched back to HarryPotter!");
}

async function testNewBehavior() {
  console.log("\n✅ Testing NEW Behavior (fixed):");
  console.log("===============================");
  
  const service = new MockStoryIntelligenceService();
  
  // 1. User switches to Vampire in main app
  console.log("1. User switches to Vampire in main app");
  service.setCurrentDataset('Vampire');
  await service.initializeNew('Vampire');
  console.log(`   Current dataset: ${service.getCurrentDataset()}`);
  
  // 2. User opens Story Explorer (calls initialize without parameter)
  console.log("\n2. User opens Story Explorer (calls initialize() without parameter)");
  await service.initializeNew(); // This now uses current dataset!
  console.log(`   Current dataset: ${service.getCurrentDataset()}`);
  console.log("   ✅ SUCCESS: Dataset maintained as Vampire!");
}

async function testCompleteFlow() {
  console.log("\n🔄 Testing Complete Flow:");
  console.log("========================");
  
  const service = new MockStoryIntelligenceService();
  
  console.log("1. Initial state");
  console.log(`   Current dataset: ${service.getCurrentDataset()}`);
  
  console.log("\n2. Dashboard initializes with HarryPotter");
  await service.initializeNew('HarryPotter');
  console.log(`   Current dataset: ${service.getCurrentDataset()}`);
  
  console.log("\n3. User switches to Vampire");
  service.setCurrentDataset('Vampire');
  await service.initializeNew('Vampire');
  console.log(`   Current dataset: ${service.getCurrentDataset()}`);
  
  console.log("\n4. Story Explorer opens (no parameter)");
  await service.initializeNew();
  console.log(`   Current dataset: ${service.getCurrentDataset()}`);
  
  console.log("\n5. Character Evolution opens (no parameter)");
  await service.initializeNew();
  console.log(`   Current dataset: ${service.getCurrentDataset()}`);
  
  console.log("\n✅ All components maintain Vampire dataset!");
}

// Run all tests
async function runAllTests() {
  await testOldBehavior();
  await testNewBehavior();
  await testCompleteFlow();
  
  console.log("\n🎉 Dataset behavior testing complete!");
  console.log("The fix ensures Story Explorer respects the current dataset selection.");
}

runAllTests();
