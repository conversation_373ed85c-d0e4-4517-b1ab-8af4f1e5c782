#!/bin/bash

echo "🧛 Compiling All 30-Chapter Vampire Data"
echo "========================================"

# Step 1: Process individual chapters for chapter-by-chapter view
echo "📖 Step 1: Processing individual chapters (1-30)..."
python exp_scripts/process_vampire_30_chapters.py
if [ $? -ne 0 ]; then
    echo "❌ Error processing individual chapters"
    exit 1
fi

# Step 2: Process global vampire data
echo "🌍 Step 2: Processing global data from 30-chapter combined book..."
python exp_scripts/process_vampire_30_global.py
if [ $? -ne 0 ]; then
    echo "❌ Error processing global data"
    exit 1
fi

# Step 3: Spatialize chapter networks
echo "🎯 Step 3: Spatializing chapter networks (1-30)..."
node spatialize-vampire-30-chapters.js
if [ $? -ne 0 ]; then
    echo "❌ Error spatializing chapter networks"
    exit 1
fi

# Step 4: Spatialize global network
echo "🌐 Step 4: Spatializing global network..."
node spatialize-vampire-30-global.js
if [ $? -ne 0 ]; then
    echo "❌ Error spatializing global network"
    exit 1
fi

echo ""
echo "✅ All 30-chapter vampire data compilation completed successfully!"
echo ""
echo "📋 Summary:"
echo "- Global view: Uses consolidated data from c_30_combined_book.json"
echo "- Chapter views: Use individual chapter data (c1.json through c30.json)"
echo "- All networks are properly spatialized"
echo "- Generated files use 'Vampire30' prefix to distinguish from 10-chapter version"
echo ""
echo "🎉 The 30-chapter Vampire knowledge graph is ready!"
echo "   - Global view shows consolidated characters and relationships across all 30 chapters"
echo "   - Chapter-by-chapter view shows detailed interactions per chapter"
echo "   - Both views are fully functional and optimized"
