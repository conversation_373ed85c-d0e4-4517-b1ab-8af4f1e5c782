/**
 * Story Intelligence Dashboard
 * Main interface for exploring Harry Potter story intelligence
 */

import { storyIntelligence, Character, Relationship } from './story-intelligence';
import { characterEvolution } from './character-evolution';
import { storyExplorer } from './story-explorer';
import { storySuccessDashboard } from './story-success-dashboard';

interface DashboardState {
  currentView: 'overview' | 'character' | 'relationship' | 'themes' | 'timeline' | 'evolution' | 'explorer' | 'success';
  selectedCharacter: string | null;
  selectedRelationship: string | null;
  selectedChapter: number | null;
  currentDataset: string;
  isLoading: boolean;
}

class StoryDashboard {
  private state: DashboardState = {
    currentView: 'overview',
    selectedCharacter: null,
    selectedRelationship: null,
    selectedChapter: null,
    currentDataset: 'HarryPotter',
    isLoading: true
  };

  private dashboardContainer: HTMLElement | null = null;
  private listeners: Map<string, Function[]> = new Map();

  async initialize(containerId: string, dataset: string = 'HarryPotter'): Promise<void> {
    console.log(`🚀 Initializing Story Dashboard for ${dataset}...`);

    this.dashboardContainer = document.getElementById(containerId);
    if (!this.dashboardContainer) {
      throw new Error(`Dashboard container ${containerId} not found`);
    }

    // Update dataset if different
    if (this.state.currentDataset !== dataset) {
      this.state.currentDataset = dataset;
      this.state.isLoading = true;
    }

    try {
      // Initialize story intelligence service with dataset
      await storyIntelligence.initialize(dataset);
      this.state.isLoading = false;

      this.render();
      this.setupEventListeners();

      console.log(`✅ Story Dashboard initialized successfully for ${dataset}`);
    } catch (error) {
      console.error(`❌ Failed to initialize Story Dashboard for ${dataset}:`, error);
      this.renderError(error.message);
      throw error;
    }
  }

  async switchDataset(dataset: string): Promise<void> {
    if (this.state.currentDataset === dataset) return;

    console.log(`🔄 Switching Story Dashboard to ${dataset}...`);
    this.state.currentDataset = dataset;
    this.state.isLoading = true;
    this.state.selectedCharacter = null;
    this.state.selectedRelationship = null;
    this.state.currentView = 'overview';

    this.render(); // Show loading state

    try {
      await storyIntelligence.initialize(dataset);
      this.state.isLoading = false;
      this.render();
      console.log(`✅ Story Dashboard switched to ${dataset}`);
    } catch (error) {
      console.error(`❌ Failed to switch Story Dashboard to ${dataset}:`, error);
      this.renderError(error.message);
    }
  }

  private render(): void {
    if (!this.dashboardContainer) return;

    if (this.state.isLoading) {
      this.dashboardContainer.innerHTML = `
        <div class="story-dashboard">
          <div class="loading-container">
            <div class="loading-spinner">📚</div>
            <p>Loading Story Intelligence...</p>
          </div>
        </div>
      `;
      this.applyStyles();
      return;
    }

    this.dashboardContainer.innerHTML = `
      <div class="story-dashboard">
        ${this.renderHeader()}
        ${this.renderNavigationTabs()}
        ${this.renderMainContent()}
        ${this.renderSidebar()}
      </div>
    `;

    this.applyStyles();
  }

  private renderError(message: string): void {
    if (!this.dashboardContainer) return;

    this.dashboardContainer.innerHTML = `
      <div class="story-dashboard">
        <div class="error-container">
          <div class="error-icon">❌</div>
          <h2>Failed to Load Story Intelligence</h2>
          <p>${message}</p>
          <button onclick="location.reload()" class="retry-button">Retry</button>
        </div>
      </div>
    `;
    this.applyStyles();
  }

  private renderHeader(): string {
    const metadata = storyIntelligence.getBookMetadata();
    return `
      <div class="dashboard-header">
        <div class="story-title">
          <div class="title-row">
            <h1>${metadata?.book_title || 'Story Intelligence'}</h1>
            <div class="dataset-selector">
              <select id="dashboard-dataset-select" class="dashboard-dataset-dropdown">
                <option value="HarryPotter" ${this.state.currentDataset === 'HarryPotter' ? 'selected' : ''}>Harry Potter</option>
                <option value="Vampire" ${this.state.currentDataset === 'Vampire' ? 'selected' : ''}>Vampire (10 chapters)</option>
                <option value="Vampire30" ${this.state.currentDataset === 'Vampire30' ? 'selected' : ''}>Vampire (30 chapters)</option>
              </select>
            </div>
          </div>
          <div class="story-stats">
            <span class="stat">
              <span class="stat-number">${storyIntelligence.getAllCharacters().length}</span>
              <span class="stat-label">Characters</span>
            </span>
            <span class="stat">
              <span class="stat-number">${storyIntelligence.getAllRelationships().length}</span>
              <span class="stat-label">Relationships</span>
            </span>
            <span class="stat">
              <span class="stat-number">${metadata?.chapters_analyzed || 0}</span>
              <span class="stat-label">Chapters</span>
            </span>
            <span class="stat">
              <span class="stat-number">${metadata?.overall_themes.length || 0}</span>
              <span class="stat-label">Themes</span>
            </span>
          </div>
        </div>
      </div>
    `;
  }

  private renderNavigationTabs(): string {
    const tabs = [
      { id: 'overview', label: 'Story Overview', icon: '📊' },
      { id: 'success', label: 'Success Analysis', icon: '🎯' },
      { id: 'character', label: 'Character Evolution', icon: '👤' },
      { id: 'relationship', label: 'Relationship Dynamics', icon: '🔗' },
      { id: 'themes', label: 'Narrative Analysis', icon: '🎭' },
      { id: 'timeline', label: 'Story Timeline', icon: '⏱️' },
      { id: 'evolution', label: 'Character Evolution', icon: '🧬' },
      { id: 'explorer', label: 'Story Explorer', icon: '🌟' }
    ];

    return `
      <div class="dashboard-tabs">
        ${tabs.map(tab => `
          <button class="tab-button ${this.state.currentView === tab.id ? 'active' : ''}" 
                  data-view="${tab.id}">
            <span class="tab-icon">${tab.icon}</span>
            <span class="tab-label">${tab.label}</span>
          </button>
        `).join('')}
      </div>
    `;
  }

  private renderMainContent(): string {
    switch (this.state.currentView) {
      case 'overview':
        return this.renderOverviewContent();
      case 'success':
        return this.renderSuccessContent();
      case 'character':
        return this.renderCharacterContent();
      case 'relationship':
        return this.renderRelationshipContent();
      case 'themes':
        return this.renderThemesContent();
      case 'timeline':
        return this.renderTimelineContent();
      case 'evolution':
        return this.renderEvolutionContent();
      case 'explorer':
        return this.renderExplorerContent();
      default:
        return this.renderOverviewContent();
    }
  }

  private renderOverviewContent(): string {
    const metadata = storyIntelligence.getBookMetadata();
    const networkAnalysis = storyIntelligence.getNetworkAnalysis();
    const insights = storyIntelligence.getNarrativeInsights();

    return `
      <div class="main-content overview-content">
        <div class="content-grid">
          <div class="overview-section themes-section">
            <h3>📚 Major Themes</h3>
            <div class="theme-list">
              ${metadata?.overall_themes.map(theme => `
                <div class="theme-item">${theme}</div>
              `).join('') || ''}
            </div>
          </div>

          <div class="overview-section plot-arcs-section">
            <h3>🎬 Plot Arcs</h3>
            <div class="plot-arc-list">
              ${metadata?.major_plot_arcs.map(arc => `
                <div class="plot-arc-item">${arc}</div>
              `).join('') || ''}
            </div>
          </div>

          <div class="overview-section influential-characters">
            <h3>⭐ Most Influential Characters</h3>
            <div class="character-ranking">
              ${storyIntelligence.getMostInfluentialCharacters(5).map(char => {
                const character = storyIntelligence.getCharacter(char.character_id);
                return `
                  <div class="ranking-item" data-character="${char.character_id}">
                    <span class="character-name">${character?.character_name || char.character_id}</span>
                    <span class="influence-score">${char.network_influence}/10</span>
                  </div>
                `;
              }).join('')}
            </div>
          </div>

          <div class="overview-section relationship-patterns">
            <h3>🔄 Relationship Patterns</h3>
            <div class="pattern-list">
              ${networkAnalysis?.relationship_patterns.slice(0, 3).map(pattern => `
                <div class="pattern-item">${pattern}</div>
              `).join('') || ''}
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private renderSuccessContent(): string {
    return `
      <div class="main-content success-content">
        <div id="success-dashboard-container"></div>
      </div>
    `;
  }

  private renderCharacterContent(): string {
    const characters = storyIntelligence.getAllCharacters()
      .sort((a, b) => b.overall_importance - a.overall_importance);

    return `
      <div class="main-content character-content">
        <div class="character-explorer">
          <div class="character-list">
            <h3>Character Evolution</h3>
            <div class="character-grid">
              ${characters.slice(0, 12).map(char => `
                <div class="character-card" data-character="${char.character_id}">
                  <div class="character-header">
                    <h4>${char.character_name}</h4>
                    <span class="archetype">${char.character_archetype}</span>
                  </div>
                  <div class="character-stats">
                    <div class="stat-item">
                      <span class="stat-label">Importance</span>
                      <div class="stat-bar">
                        <div class="stat-fill" style="width: ${char.overall_importance * 10}%"></div>
                      </div>
                    </div>
                    <div class="stat-item">
                      <span class="stat-label">Chapters</span>
                      <span class="stat-value">${char.total_chapters_present}</span>
                    </div>
                  </div>
                  <div class="character-evolution-preview">
                    <span class="evolution-trajectory ${char.character_evolution.growth_trajectory}">
                      ${char.character_evolution.growth_trajectory === 'overall' ? '📈' : 
                        char.character_evolution.growth_trajectory === 'positive' ? '⬆️' : '🔄'}
                      ${char.character_evolution.growth_trajectory}
                    </span>
                  </div>
                </div>
              `).join('')}
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private renderRelationshipContent(): string {
    const strongestRelationships = storyIntelligence.getStrongestRelationships(8);

    return `
      <div class="main-content relationship-content">
        <div class="relationship-explorer">
          <h3>Relationship Dynamics</h3>
          <div class="relationship-grid">
            ${strongestRelationships.map(rel => {
              const charA = storyIntelligence.getCharacter(rel.character_a_id);
              const charB = storyIntelligence.getCharacter(rel.character_b_id);
              return `
                <div class="relationship-card" data-relationship="${rel.relationship_id}">
                  <div class="relationship-header">
                    <div class="character-pair">
                      <span class="character-a">${charA?.character_name || rel.character_a_id}</span>
                      <span class="relationship-connector">↔</span>
                      <span class="character-b">${charB?.character_name || rel.character_b_id}</span>
                    </div>
                    <span class="relationship-type">${rel.relationship_classification}</span>
                  </div>
                  <div class="relationship-strength">
                    <div class="strength-bar">
                      <div class="strength-fill" style="width: ${rel.overall_strength * 10}%"></div>
                    </div>
                    <span class="strength-value">${rel.overall_strength}/10</span>
                  </div>
                  <div class="relationship-summary">
                    ${rel.relationship_summary.substring(0, 120)}...
                  </div>
                  <div class="relationship-stability ${rel.relationship_stability}">
                    ${rel.relationship_stability === 'stable' ? '🟢' : 
                      rel.relationship_stability === 'evolving' ? '🟡' : '🔴'}
                    ${rel.relationship_stability}
                  </div>
                </div>
              `;
            }).join('')}
          </div>
        </div>
      </div>
    `;
  }

  private renderThemesContent(): string {
    const themes = storyIntelligence.getOverallThemes();
    const agencyRanking = storyIntelligence.getCharacterAgencyRanking();

    return `
      <div class="main-content themes-content">
        <div class="themes-explorer">
          <div class="themes-grid">
            <div class="themes-section">
              <h3>🎭 Narrative Themes</h3>
              <div class="theme-analysis">
                ${themes.map(theme => `
                  <div class="theme-analysis-item">
                    <h4>${theme}</h4>
                    <div class="theme-progress">
                      <div class="theme-bar"></div>
                    </div>
                  </div>
                `).join('')}
              </div>
            </div>

            <div class="agency-section">
              <h3>🎯 Character Agency Ranking</h3>
              <div class="agency-ranking">
                ${agencyRanking.slice(0, 8).map((char, index) => {
                  const character = storyIntelligence.getCharacter(char.character_id);
                  return `
                    <div class="agency-item">
                      <span class="rank">#${index + 1}</span>
                      <span class="character-name">${character?.character_name || char.character_id}</span>
                      <span class="agency-score">${char.agency_score}/10</span>
                      <span class="influence-type ${char.influence_type}">${char.influence_type}</span>
                    </div>
                  `;
                }).join('')}
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private renderTimelineContent(): string {
    return `
      <div class="main-content timeline-content">
        <div class="timeline-explorer">
          <h3>⏱️ Story Timeline</h3>
          <div class="timeline-placeholder">
            <p>Interactive timeline visualization coming soon...</p>
            <p>This will show character evolution and relationship changes over time.</p>
          </div>
        </div>
      </div>
    `;
  }

  private renderSidebar(): string {
    return `
      <div class="dashboard-sidebar">
        <div class="sidebar-content">
          <h4>Quick Insights</h4>
          <div class="insight-item">
            <span class="insight-icon">📈</span>
            <span class="insight-text">Character development peaks in chapters 4-6</span>
          </div>
          <div class="insight-item">
            <span class="insight-icon">🔗</span>
            <span class="insight-text">Strongest relationships show high emotional intensity</span>
          </div>
          <div class="insight-item">
            <span class="insight-icon">🎭</span>
            <span class="insight-text">War themes dominate narrative progression</span>
          </div>
        </div>
      </div>
    `;
  }

  private setupEventListeners(): void {
    if (!this.dashboardContainer) return;

    // Dataset selector
    const datasetSelect = this.dashboardContainer.querySelector('#dashboard-dataset-select') as HTMLSelectElement;
    if (datasetSelect) {
      datasetSelect.addEventListener('change', async (e) => {
        const newDataset = (e.target as HTMLSelectElement).value;
        await this.switchDataset(newDataset);
      });
    }

    // Tab navigation
    this.dashboardContainer.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;

      if (target.closest('.tab-button')) {
        const tabButton = target.closest('.tab-button') as HTMLElement;
        const view = tabButton.dataset.view as DashboardState['currentView'];
        if (view) {
          this.switchView(view);
        }
      }

      // Character card clicks
      if (target.closest('.character-card')) {
        const characterCard = target.closest('.character-card') as HTMLElement;
        const characterId = characterCard.dataset.character;
        if (characterId) {
          this.selectCharacter(characterId);
        }
      }

      // Relationship card clicks
      if (target.closest('.relationship-card')) {
        const relationshipCard = target.closest('.relationship-card') as HTMLElement;
        const relationshipId = relationshipCard.dataset.relationship;
        if (relationshipId) {
          this.selectRelationship(relationshipId);
        }
      }
    });
  }

  private async switchView(view: DashboardState['currentView']): Promise<void> {
    this.state.currentView = view;
    this.render();

    // Initialize new components when switching to their views
    if (view === 'evolution') {
      await this.initializeCharacterEvolution();
    } else if (view === 'explorer') {
      await this.initializeStoryExplorer();
    } else if (view === 'success') {
      await this.initializeSuccessDashboard();
    }
  }

  private async initializeCharacterEvolution(): Promise<void> {
    try {
      await characterEvolution.initialize('character-evolution-container');
    } catch (error) {
      console.error('Failed to initialize Character Evolution:', error);
    }
  }

  private async initializeStoryExplorer(): Promise<void> {
    try {
      await storyExplorer.initialize('story-explorer-container');
    } catch (error) {
      console.error('Failed to initialize Story Explorer:', error);
    }
  }

  private selectCharacter(characterId: string): void {
    this.state.selectedCharacter = characterId;
    this.showCharacterDetails(characterId);
  }

  private selectRelationship(relationshipId: string): void {
    this.state.selectedRelationship = relationshipId;
    this.showRelationshipDetails(relationshipId);
  }

  private showCharacterDetails(characterId: string): void {
    const character = storyIntelligence.getCharacter(characterId);
    if (!character) return;

    // Create modal or detailed view
    const modal = document.createElement('div');
    modal.className = 'character-detail-modal';
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h2>${character.character_name}</h2>
          <button class="close-modal">×</button>
        </div>
        <div class="modal-body">
          <div class="character-evolution-detail">
            <h3>Character Evolution</h3>
            <p><strong>Initial State:</strong> ${character.character_evolution.initial_state}</p>
            <p><strong>Final State:</strong> ${character.character_evolution.final_state}</p>

            <h4>Major Turning Points:</h4>
            ${character.character_evolution.major_turning_points.map(point => `
              <div class="turning-point">
                <strong>Chapter ${point.chapter}:</strong> ${point.event}
                <p><em>${point.impact}</em></p>
              </div>
            `).join('')}
          </div>

          <div class="character-motivations">
            <h3>Core Motivations</h3>
            <ul>
              ${character.core_motivations.map(motivation => `<li>${motivation}</li>`).join('')}
            </ul>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    // Close modal handler
    modal.querySelector('.close-modal')?.addEventListener('click', () => {
      document.body.removeChild(modal);
    });

    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        document.body.removeChild(modal);
      }
    });
  }

  private showRelationshipDetails(relationshipId: string): void {
    const relationship = storyIntelligence.getRelationship(relationshipId);
    if (!relationship) return;

    const charA = storyIntelligence.getCharacter(relationship.character_a_id);
    const charB = storyIntelligence.getCharacter(relationship.character_b_id);

    const modal = document.createElement('div');
    modal.className = 'relationship-detail-modal';
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h2>${charA?.character_name} ↔ ${charB?.character_name}</h2>
          <button class="close-modal">×</button>
        </div>
        <div class="modal-body">
          <div class="relationship-description">
            ${relationship.relationship_summary}
          </div>

          <div class="relationship-evolution-detail">
            <h3>Relationship Evolution</h3>

            <div class="evolution-status">
              <p><strong>Initial Dynamic:</strong> ${relationship.relationship_evolution.initial_dynamic}</p>
              <p><strong>Current Status:</strong> ${relationship.relationship_evolution.current_status}</p>
            </div>

            <h4>Key Developments:</h4>
            <ul>
              ${relationship.relationship_evolution.key_developments.map(dev => `
                <li>
                  <strong>Chapter ${dev.chapter}: ${dev.event}</strong>
                  <em>${dev.new_dynamic}</em>
                  <span class="change-indicator ${dev.relationship_change > 0 ? 'positive' : 'negative'}">
                    ${dev.relationship_change > 0 ? '+' : ''}${dev.relationship_change}
                  </span>
                </li>
              `).join('')}
            </ul>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    modal.querySelector('.close-modal')?.addEventListener('click', () => {
      document.body.removeChild(modal);
    });

    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        document.body.removeChild(modal);
      }
    });
  }

  private applyStyles(): void {
    if (!document.getElementById('story-dashboard-styles')) {
      const styles = document.createElement('style');
      styles.id = 'story-dashboard-styles';
      styles.textContent = `
        .story-dashboard {
          display: flex;
          flex-direction: column;
          height: 100vh;
          background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
          color: #ffffff;
          font-family: 'DejaVu Sans Mono', monospace;
        }

        .dashboard-header {
          padding: 20px 30px;
          background: rgba(255, 255, 255, 0.05);
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .title-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 15px;
        }

        .story-title h1 {
          margin: 0;
          font-size: 2.2em;
          font-weight: bold;
          background: linear-gradient(45deg, #ffd700, #ff6b6b, #4ecdc4);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .dataset-selector {
          margin-left: 20px;
        }

        .dashboard-dataset-dropdown {
          padding: 8px 16px;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 6px;
          color: #ffffff;
          font-family: inherit;
          font-size: 14px;
          cursor: pointer;
          outline: none;
          transition: all 0.3s ease;
          min-width: 140px;
        }

        .dashboard-dataset-dropdown:hover {
          background: rgba(255, 255, 255, 0.15);
          border-color: #4ecdc4;
        }

        .dashboard-dataset-dropdown:focus {
          border-color: #4ecdc4;
          box-shadow: 0 0 10px rgba(78, 205, 196, 0.3);
        }

        .dashboard-dataset-dropdown option {
          background: #1a1a2e;
          color: #ffffff;
          padding: 8px;
        }

        .story-stats {
          display: flex;
          gap: 30px;
        }

        .stat {
          display: flex;
          flex-direction: column;
          align-items: center;
        }

        .stat-number {
          font-size: 1.8em;
          font-weight: bold;
          color: #4ecdc4;
        }

        .stat-label {
          font-size: 0.9em;
          color: #aaa;
          text-transform: uppercase;
          letter-spacing: 1px;
        }

        .dashboard-tabs {
          display: flex;
          background: rgba(255, 255, 255, 0.03);
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .tab-button {
          flex: 1;
          padding: 15px 20px;
          background: none;
          border: none;
          color: #aaa;
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          font-family: inherit;
        }

        .tab-button:hover {
          background: rgba(255, 255, 255, 0.05);
          color: #fff;
        }

        .tab-button.active {
          background: rgba(78, 205, 196, 0.2);
          color: #4ecdc4;
          border-bottom: 2px solid #4ecdc4;
        }

        .tab-icon {
          font-size: 1.2em;
        }

        .main-content {
          flex: 1;
          padding: 30px;
          overflow-y: auto;
        }

        .content-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 30px;
        }

        .overview-section {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 12px;
          padding: 25px;
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .overview-section h3 {
          margin: 0 0 20px 0;
          color: #4ecdc4;
          font-size: 1.3em;
        }

        .theme-item, .plot-arc-item, .pattern-item {
          background: rgba(255, 255, 255, 0.08);
          padding: 12px 16px;
          margin: 8px 0;
          border-radius: 8px;
          border-left: 3px solid #ffd700;
        }

        .character-ranking .ranking-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px 0;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
          cursor: pointer;
          transition: background 0.2s ease;
        }

        .character-ranking .ranking-item:hover {
          background: rgba(255, 255, 255, 0.05);
        }

        .influence-score {
          color: #ff6b6b;
          font-weight: bold;
        }

        .character-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 20px;
        }

        .character-card {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 12px;
          padding: 20px;
          border: 1px solid rgba(255, 255, 255, 0.1);
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .character-card:hover {
          background: rgba(255, 255, 255, 0.08);
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .character-header h4 {
          margin: 0 0 5px 0;
          color: #fff;
          font-size: 1.2em;
        }

        .archetype {
          color: #4ecdc4;
          font-size: 0.9em;
          text-transform: capitalize;
        }

        .character-stats {
          margin: 15px 0;
        }

        .stat-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin: 8px 0;
        }

        .stat-bar {
          width: 100px;
          height: 6px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 3px;
          overflow: hidden;
        }

        .stat-fill {
          height: 100%;
          background: linear-gradient(90deg, #4ecdc4, #ffd700);
          transition: width 0.3s ease;
        }

        .evolution-trajectory {
          display: inline-flex;
          align-items: center;
          gap: 5px;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 0.8em;
          text-transform: capitalize;
        }

        .evolution-trajectory.overall {
          background: rgba(78, 205, 196, 0.2);
          color: #4ecdc4;
        }

        .evolution-trajectory.positive {
          background: rgba(76, 175, 80, 0.2);
          color: #4caf50;
        }

        .relationship-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
          gap: 20px;
        }

        .relationship-card {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 12px;
          padding: 20px;
          border: 1px solid rgba(255, 255, 255, 0.1);
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .relationship-card:hover {
          background: rgba(255, 255, 255, 0.08);
          transform: translateY(-2px);
        }

        .character-pair {
          display: flex;
          align-items: center;
          gap: 10px;
          margin-bottom: 8px;
        }

        .relationship-connector {
          color: #4ecdc4;
          font-weight: bold;
        }

        .relationship-type {
          color: #ffd700;
          font-size: 0.9em;
          text-transform: capitalize;
        }

        .strength-bar {
          width: 100%;
          height: 8px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 4px;
          overflow: hidden;
          margin: 10px 0;
        }

        .strength-fill {
          height: 100%;
          background: linear-gradient(90deg, #ff6b6b, #ffd700);
          transition: width 0.3s ease;
        }

        .relationship-stability.stable {
          color: #4caf50;
        }

        .relationship-stability.evolving {
          color: #ffd700;
        }

        .themes-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 30px;
        }

        .themes-section, .agency-section {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 12px;
          padding: 25px;
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .theme-analysis-item {
          margin: 15px 0;
        }

        .theme-analysis-item h4 {
          margin: 0 0 8px 0;
          color: #ffd700;
        }

        .theme-progress {
          height: 6px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 3px;
          overflow: hidden;
        }

        .theme-bar {
          height: 100%;
          background: linear-gradient(90deg, #4ecdc4, #ff6b6b);
          width: 75%;
          transition: width 0.3s ease;
        }

        .agency-ranking {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .agency-item {
          display: flex;
          align-items: center;
          gap: 15px;
          padding: 10px;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 8px;
          border-left: 3px solid #4ecdc4;
        }

        .rank {
          font-weight: bold;
          color: #ffd700;
          min-width: 30px;
        }

        .character-name {
          flex: 1;
          color: #fff;
        }

        .agency-score {
          color: #4ecdc4;
          font-weight: bold;
        }

        .influence-type {
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 0.8em;
          text-transform: capitalize;
        }

        .influence-type.direct {
          background: rgba(76, 175, 80, 0.2);
          color: #4caf50;
        }

        .influence-type.indirect {
          background: rgba(255, 193, 7, 0.2);
          color: #ffc107;
        }

        .influence-type.catalyst {
          background: rgba(255, 87, 34, 0.2);
          color: #ff5722;
        }

        .timeline-placeholder {
          text-align: center;
          padding: 60px 20px;
          color: #aaa;
        }

        .timeline-placeholder p {
          margin: 10px 0;
          font-size: 1.1em;
        }

        /* Modal Styles */
        .character-detail-modal, .relationship-detail-modal {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.8);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 2000;
        }

        .modal-content {
          background: linear-gradient(135deg, #1a1a2e, #16213e);
          border-radius: 16px;
          max-width: 900px;
          width: 90vw;
          max-height: 85vh;
          overflow-y: auto;
          border: 1px solid rgba(255, 255, 255, 0.2);
          box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        }

        .modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 25px 35px;
          border-bottom: 2px solid rgba(78, 205, 196, 0.3);
          background: rgba(255, 255, 255, 0.02);
        }

        .modal-header h2 {
          margin: 0;
          color: #4ecdc4;
          font-size: 1.8em;
          font-weight: 600;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .close-modal {
          background: none;
          border: none;
          color: #fff;
          font-size: 24px;
          cursor: pointer;
          padding: 0;
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .close-modal:hover {
          color: #ff6b6b;
        }

        .modal-body {
          padding: 30px 35px;
          color: #ffffff;
        }

        .modal-body h3 {
          color: #4ecdc4;
          margin-top: 30px;
          margin-bottom: 20px;
          font-size: 1.4em;
          font-weight: 600;
          border-bottom: 1px solid rgba(78, 205, 196, 0.3);
          padding-bottom: 8px;
        }

        .modal-body h4 {
          color: #4ecdc4;
          margin-top: 25px;
          margin-bottom: 15px;
          font-size: 1.2em;
          font-weight: 500;
        }

        .modal-body p {
          color: #e8e8e8;
          line-height: 1.7;
          margin-bottom: 15px;
          font-size: 1.05em;
        }

        .modal-body strong {
          color: #ffffff;
          font-weight: 600;
        }

        .relationship-description {
          background: rgba(78, 205, 196, 0.08);
          padding: 20px;
          border-radius: 10px;
          border-left: 4px solid #4ecdc4;
          margin-bottom: 25px;
          font-size: 1.1em;
          line-height: 1.7;
        }

        .modal-body ul {
          color: #e8e8e8;
          padding-left: 0;
          list-style: none;
        }

        .modal-body li {
          margin-bottom: 15px;
          line-height: 1.6;
          color: #e8e8e8;
          background: rgba(255, 255, 255, 0.03);
          padding: 15px 20px;
          border-radius: 8px;
          border-left: 3px solid #4ecdc4;
          position: relative;
        }

        .modal-body li strong {
          display: block;
          margin-bottom: 8px;
          font-size: 1.1em;
          color: #4ecdc4;
        }

        .modal-body li em {
          color: #b8b8b8;
          font-style: italic;
          font-size: 0.95em;
          display: block;
          margin-top: 5px;
        }

        .evolution-status {
          background: rgba(255, 255, 255, 0.02);
          padding: 15px 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }

        .evolution-status p {
          margin-bottom: 10px;
        }

        .change-indicator {
          position: absolute;
          top: 15px;
          right: 20px;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 0.85em;
          font-weight: 600;
        }

        .change-indicator.positive {
          background: rgba(76, 175, 80, 0.2);
          color: #4caf50;
          border: 1px solid rgba(76, 175, 80, 0.3);
        }

        .change-indicator.negative {
          background: rgba(244, 67, 54, 0.2);
          color: #f44336;
          border: 1px solid rgba(244, 67, 54, 0.3);
        }

        /* Scrollable content styles */
        .scrollable-content {
          height: calc(100vh - 200px);
          overflow-y: auto;
          overflow-x: hidden;
          padding-right: 10px;
        }

        .scrollable-content::-webkit-scrollbar {
          width: 8px;
        }

        .scrollable-content::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
        }

        .scrollable-content::-webkit-scrollbar-thumb {
          background: linear-gradient(135deg, #4ecdc4, #44a08d);
          border-radius: 4px;
        }

        .scrollable-content::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(135deg, #44a08d, #4ecdc4);
        }

        /* Responsive design for smaller screens */
        @media (max-width: 768px) {
          .modal-content {
            max-width: 95vw;
            width: 95vw;
            margin: 20px auto;
          }

          .modal-header {
            padding: 20px 25px;
          }

          .modal-header h2 {
            font-size: 1.5em;
          }

          .modal-body {
            padding: 25px;
          }

          .change-indicator {
            position: static;
            display: inline-block;
            margin-top: 10px;
          }

          .scrollable-content {
            height: calc(100vh - 150px);
          }
        }

        .turning-point, .relationship-development {
          background: rgba(255, 255, 255, 0.05);
          padding: 15px;
          margin: 10px 0;
          border-radius: 8px;
          border-left: 3px solid #ffd700;
          color: #e0e0e0;
        }

        .turning-point strong, .relationship-development strong {
          color: #4ecdc4;
        }

        .turning-point p, .relationship-development p {
          color: #d0d0d0;
          margin-top: 8px;
        }

        .turning-point em, .relationship-development em {
          color: #b0b0b0;
          font-style: italic;
        }

        .change-indicator {
          float: right;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 0.8em;
          font-weight: bold;
        }

        .change-indicator.positive {
          background: rgba(76, 175, 80, 0.2);
          color: #4caf50;
        }

        .change-indicator.negative {
          background: rgba(244, 67, 54, 0.2);
          color: #f44336;
        }

        /* Loading and Error States */
        .loading-container, .error-container {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100vh;
          text-align: center;
        }

        .loading-spinner {
          font-size: 4em;
          animation: pulse 2s infinite;
        }

        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.5; }
        }

        .error-icon {
          font-size: 4em;
          margin-bottom: 20px;
        }

        .error-container h2 {
          color: #ff6b6b;
          margin: 20px 0;
        }

        .error-container p {
          color: #aaa;
          margin: 10px 0 30px 0;
        }

        .retry-button {
          background: linear-gradient(135deg, #4ecdc4, #44a08d);
          border: none;
          border-radius: 8px;
          padding: 12px 24px;
          color: white;
          font-weight: bold;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .retry-button:hover {
          background: linear-gradient(135deg, #44a08d, #4ecdc4);
          transform: translateY(-1px);
        }
      `;
      document.head.appendChild(styles);
    }
  }

  private renderEvolutionContent(): string {
    return `
      <div class="evolution-content scrollable-content">
        <div id="character-evolution-container"></div>
      </div>
    `;
  }

  private renderExplorerContent(): string {
    return `
      <div class="explorer-content scrollable-content">
        <div id="story-explorer-container"></div>
      </div>
    `;
  }

  private async initializeSuccessDashboard(): Promise<void> {
    try {
      await storySuccessDashboard.initialize('success-dashboard-container');
      console.log('✅ Success Dashboard component initialized');
    } catch (error) {
      console.error('❌ Failed to initialize Success Dashboard:', error);
    }
  }
}

// Create singleton instance
export const storyDashboard = new StoryDashboard();
