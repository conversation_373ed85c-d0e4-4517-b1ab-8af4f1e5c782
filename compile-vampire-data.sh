#!/bin/bash

echo "🧛 Compiling All Vampire Data"
echo "=============================="

# Step 1: Process individual chapters for chapter-by-chapter view
echo "📖 Step 1: Processing individual chapters..."
python exp_scripts/process_vampire_chapters.py
if [ $? -ne 0 ]; then
    echo "❌ Error processing individual chapters"
    exit 1
fi

# Step 2: Process global vampire data
echo "🌍 Step 2: Processing global data from combined book..."
python exp_scripts/process_vampire_global.py
if [ $? -ne 0 ]; then
    echo "❌ Error processing global data"
    exit 1
fi

# Step 3: Spatialize chapter networks
echo "🎯 Step 3: Spatializing chapter networks..."
node spatialize-vampire-chapters.js
if [ $? -ne 0 ]; then
    echo "❌ Error spatializing chapter networks"
    exit 1
fi

# Step 4: Spatialize global network
echo "🌐 Step 4: Spatializing global network..."
node spatialize-vampire-global.js
if [ $? -ne 0 ]; then
    echo "❌ Error spatializing global network"
    exit 1
fi

echo ""
echo "✅ All vampire data compilation completed successfully!"
echo ""
echo "📋 Summary:"
echo "- Global view: Uses consolidated data from c1_10_combined_book.json"
echo "- Chapter views: Use individual chapter data (c1.json through c10.json)"
echo "- All networks are properly spatialized"
echo ""
echo "🎉 The Vampire knowledge graph is ready!"
echo "   - Global view shows consolidated characters and relationships across all chapters"
echo "   - Chapter-by-chapter view shows detailed interactions per chapter"
echo "   - Both views are fully functional and optimized"
