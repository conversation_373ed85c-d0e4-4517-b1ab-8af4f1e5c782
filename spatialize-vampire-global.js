const fs = require('fs');
const pako = require('pako');
const graphology = require('graphology');
const layouts = require('graphology-layout');
const forceAtlas2 = require('graphology-layout-forceatlas2');

// Color utility function (same as in utils.ts)
function lightenColor(color, amount) {
  const usePound = color[0] === "#";
  const col = usePound ? color.slice(1) : color;
  const num = parseInt(col, 16);
  let r = (num >> 16) + amount;
  let g = (num >> 8 & 0x00FF) + amount;
  let b = (num & 0x0000FF) + amount;
  r = r > 255 ? 255 : r < 0 ? 0 : r;
  g = g > 255 ? 255 : g < 0 ? 0 : g;
  b = b > 255 ? 255 : b < 0 ? 0 : b;
  return (usePound ? "#" : "") + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
}

function readPakoJSON(filename) {
  console.log("Reading " + filename + " ...");
  const pakofile = fs.readFileSync(filename, {flag:'r'});
  return graphology.Graph.from(JSON.parse(pako.inflate(pakofile, {to: "string"})));
}

function writePakoJSON(graph, filename) {
  console.log("Writing " + filename + " ...");
  fs.writeFileSync(filename, pako.deflate(JSON.stringify(graph)));
}

function spatializeVampireGlobal(filename) {
  const graph = readPakoJSON(filename);
  
  console.log(`Spatializing ${filename} with ${graph.order} nodes and ${graph.size} edges...`);
  
  // Set initial circular positions
  const circularPositions = layouts.circular(graph, { scale: 100 });
  
  graph.forEachNode(node => {
    const importance = graph.getNodeAttribute(node, 'overall_importance') || 1;
    const stories = graph.getNodeAttribute(node, 'stories') || 1;
    const size = Math.pow(importance * stories, 0.4) * 3; // Adjusted for vampire character count

    graph.mergeNodeAttributes(node, {
      x: circularPositions[node].x,
      y: circularPositions[node].y,
      size: size
    });
  });
  
  // Apply ForceAtlas2 layout for better positioning
  const settings = {
    iterations: 150,
    settings: {
      gravity: 0.3,
      scalingRatio: 15,
      strongGravityMode: true,
      barnesHutOptimize: false
    }
  };

  forceAtlas2.assign(graph, settings);

  // Set colors based on communities
  const communities = {};
  const colors = [
    '#e74c3c', '#3498db', '#2ecc71', '#f39c12', '#9b59b6',
    '#1abc9c', '#34495e', '#e67e22', '#95a5a6', '#f1c40f'
  ];

  graph.forEachNode(node => {
    const community = graph.getNodeAttribute(node, 'community') || 0;
    const baseColor = colors[community % colors.length];
    const lightColor = lightenColor(baseColor, 40);
    
    communities[community] = baseColor;
    
    graph.mergeNodeAttributes(node, {
      color: baseColor,
      borderColor: lightColor,
      highlightColor: lightColor
    });
  });

  // Write back to file
  writePakoJSON(graph, filename);
}

// Process the global Vampire file
const globalFilename = 'data/Vampire_characters_by_stories_full_processed.json.gz';
if (fs.existsSync(globalFilename)) {
  spatializeVampireGlobal(globalFilename);
} else {
  console.log(`Warning: ${globalFilename} not found, skipping...`);
}

console.log("Vampire global network spatialized!");
