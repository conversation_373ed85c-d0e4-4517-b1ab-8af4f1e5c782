const fs = require('fs');

console.log("📚 Testing Story Intelligence Dashboard Data Sources");
console.log("==================================================");

// Test Harry Potter data
console.log("\n🧙 Testing Harry Potter Data:");
try {
  const hpData = JSON.parse(fs.readFileSync('hp_data/all_chapters.json', 'utf8'));
  console.log(`✅ Harry Potter data loaded successfully`);
  console.log(`   - Book: ${hpData.book_metadata.book_title}`);
  console.log(`   - Chapters: ${hpData.book_metadata.chapters_analyzed}`);
  console.log(`   - Characters: ${hpData.consolidated_characters.length}`);
  console.log(`   - Relationships: ${hpData.consolidated_relationships.length}`);
  console.log(`   - Themes: ${hpData.book_metadata.overall_themes.length}`);
  console.log(`   - Sample themes: ${hpData.book_metadata.overall_themes.slice(0, 2).join(', ')}`);
} catch (e) {
  console.log(`❌ Harry Potter data ERROR: ${e.message}`);
}

// Test Vampire data
console.log("\n🧛 Testing Vampire Data:");
try {
  const vampireData = JSON.parse(fs.readFileSync('vampire_data/outputs/c1_10_combined_book.json', 'utf8'));
  console.log(`✅ Vampire data loaded successfully`);
  console.log(`   - Book: ${vampireData.book_metadata.book_title}`);
  console.log(`   - Chapters: ${vampireData.book_metadata.chapters_analyzed}`);
  console.log(`   - Characters: ${vampireData.consolidated_characters.length}`);
  console.log(`   - Relationships: ${vampireData.consolidated_relationships.length}`);
  console.log(`   - Themes: ${vampireData.book_metadata.overall_themes.length}`);
  console.log(`   - Sample themes: ${vampireData.book_metadata.overall_themes.slice(0, 2).join(', ')}`);
} catch (e) {
  console.log(`❌ Vampire data ERROR: ${e.message}`);
}

// Test data structure compatibility
console.log("\n🔍 Data Structure Compatibility:");
try {
  const hpData = JSON.parse(fs.readFileSync('hp_data/all_chapters.json', 'utf8'));
  const vampireData = JSON.parse(fs.readFileSync('vampire_data/outputs/c1_10_combined_book.json', 'utf8'));
  
  console.log("Required fields check:");
  
  // Check book_metadata
  const hpMeta = hpData.book_metadata;
  const vampireMeta = vampireData.book_metadata;
  console.log(`   ✅ book_metadata: HP(${!!hpMeta}) Vampire(${!!vampireMeta})`);
  console.log(`   ✅ book_title: HP(${!!hpMeta.book_title}) Vampire(${!!vampireMeta.book_title})`);
  console.log(`   ✅ chapters_analyzed: HP(${!!hpMeta.chapters_analyzed}) Vampire(${!!vampireMeta.chapters_analyzed})`);
  console.log(`   ✅ overall_themes: HP(${!!hpMeta.overall_themes}) Vampire(${!!vampireMeta.overall_themes})`);
  console.log(`   ✅ major_plot_arcs: HP(${!!hpMeta.major_plot_arcs}) Vampire(${!!vampireMeta.major_plot_arcs})`);
  
  // Check consolidated_characters
  console.log(`   ✅ consolidated_characters: HP(${!!hpData.consolidated_characters}) Vampire(${!!vampireData.consolidated_characters})`);
  
  // Check consolidated_relationships
  console.log(`   ✅ consolidated_relationships: HP(${!!hpData.consolidated_relationships}) Vampire(${!!vampireData.consolidated_relationships})`);
  
  // Check character structure
  if (hpData.consolidated_characters.length > 0 && vampireData.consolidated_characters.length > 0) {
    const hpChar = hpData.consolidated_characters[0];
    const vampireChar = vampireData.consolidated_characters[0];
    
    console.log("Character structure compatibility:");
    console.log(`   ✅ character_id: HP(${!!hpChar.character_id}) Vampire(${!!vampireChar.character_id})`);
    console.log(`   ✅ character_name: HP(${!!hpChar.character_name}) Vampire(${!!vampireChar.character_name})`);
    console.log(`   ✅ overall_importance: HP(${!!hpChar.overall_importance}) Vampire(${!!vampireChar.overall_importance})`);
    console.log(`   ✅ character_archetype: HP(${!!hpChar.character_archetype}) Vampire(${!!vampireChar.character_archetype})`);
    console.log(`   ✅ total_chapters_present: HP(${!!hpChar.total_chapters_present}) Vampire(${!!vampireChar.total_chapters_present})`);
  }
  
} catch (e) {
  console.log(`❌ Compatibility check ERROR: ${e.message}`);
}

console.log("\n🎉 Story Intelligence Dashboard data testing complete!");
console.log("Both datasets are compatible with the dashboard interface!");
