/**
 * Story Success Dashboard
 * Analyzes and visualizes key success indicators that predict market hits
 */

import { storyIntelligence } from './story-intelligence';

interface SuccessDashboardState {
  currentView: 'overview' | 'power' | 'emotional' | 'characters' | 'secrets' | 'relationships' | 'conflicts' | 'world';
  isLoading: boolean;
  successMetrics: any;
}

class StorySuccessDashboard {
  private container: HTMLElement | null = null;
  private state: SuccessDashboardState = {
    currentView: 'overview',
    isLoading: false,
    successMetrics: null
  };

  async initialize(containerId: string): Promise<void> {
    console.log('🎯 Initializing Story Success Dashboard...');

    this.container = document.getElementById(containerId);
    if (!this.container) {
      throw new Error(`Success dashboard container ${containerId} not found`);
    }

    // Always use Vampire30 dataset for success analysis
    await storyIntelligence.initialize('Vampire30');
    this.loadSuccessMetrics();
    this.render();
    this.setupEventListeners();

    console.log('✅ Story Success Dashboard initialized with Vampire30 dataset');
  }

  private async loadSuccessMetrics(): Promise<void> {
    this.state.isLoading = true;
    this.state.successMetrics = storyIntelligence.getSuccessMetrics();
    this.state.isLoading = false;
  }

  private render(): void {
    if (!this.container) return;

    this.container.innerHTML = `
      <div class="success-dashboard">
        ${this.renderHeader()}
        ${this.renderNavigationTabs()}
        ${this.renderMainContent()}
      </div>
    `;
  }

  private renderHeader(): string {
    const metadata = storyIntelligence.getBookMetadata();
    const overallScore = this.calculateOverallSuccessScore();
    
    return `
      <div class="success-header">
        <div class="success-title">
          <h1>📈 Story Success Analysis</h1>
          <div class="book-info">
            <h2>${metadata?.book_title || 'Unknown Story'}</h2>
            <div class="success-score-main">
              <div class="score-circle ${this.getScoreClass(overallScore)}">
                <span class="score-number">${overallScore}</span>
                <span class="score-label">Success Score</span>
              </div>
              <div class="score-interpretation">
                ${this.getSuccessInterpretation(overallScore)}
              </div>
            </div>
          </div>
        </div>
        
        <div class="success-summary">
          <div class="key-indicators">
            ${this.renderKeyIndicators()}
          </div>
        </div>
      </div>
    `;
  }

  private calculateOverallSuccessScore(): number {
    if (!this.state.successMetrics) return 0;
    
    const metrics = this.state.successMetrics;
    const scores = [
      metrics.powerProgression?.successScore || 0,
      metrics.emotionalIntensity?.successScore || 0,
      metrics.characterArchetypes?.successScore || 0,
      metrics.secretComplexity?.successScore || 0,
      metrics.relationshipVolatility?.successScore || 0,
      metrics.conflictEscalation?.successScore || 0,
      metrics.worldBuildingDepth?.successScore || 0
    ];
    
    return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
  }

  private getScoreClass(score: number): string {
    if (score >= 80) return 'score-excellent';
    if (score >= 60) return 'score-good';
    if (score >= 40) return 'score-average';
    return 'score-poor';
  }

  private getSuccessInterpretation(score: number): string {
    if (score >= 80) return '🚀 Bestseller Potential - This story has all the key success indicators!';
    if (score >= 60) return '⭐ Strong Market Appeal - Very promising with minor improvements needed';
    if (score >= 40) return '📚 Moderate Potential - Good foundation but needs significant enhancement';
    return '⚠️ Low Market Potential - Major structural issues need addressing';
  }

  private renderKeyIndicators(): string {
    if (!this.state.successMetrics) return '';

    const indicators = [
      {
        name: 'Power Progression',
        score: this.state.successMetrics.powerProgression?.successScore || 0,
        icon: '⚡',
        key: 'power',
        subtitle: this.getPowerProgressionSubtitle(),
        highlight: this.getPowerProgressionHighlight()
      },
      {
        name: 'Emotional Peaks',
        score: this.state.successMetrics.emotionalIntensity?.successScore || 0,
        icon: '💥',
        key: 'emotional',
        subtitle: this.getEmotionalIntensitySubtitle(),
        highlight: this.getEmotionalIntensityHighlight()
      },
      {
        name: 'Character Balance',
        score: this.state.successMetrics.characterArchetypes?.successScore || 0,
        icon: '👥',
        key: 'characters',
        subtitle: this.getCharacterArchetypesSubtitle(),
        highlight: this.getCharacterArchetypesHighlight()
      },
      {
        name: 'Secret Complexity',
        score: this.state.successMetrics.secretComplexity?.successScore || 0,
        icon: '🔐',
        key: 'secrets',
        subtitle: this.getSecretComplexitySubtitle(),
        highlight: this.getSecretComplexityHighlight()
      },
      {
        name: 'Relationship Drama',
        score: this.state.successMetrics.relationshipVolatility?.successScore || 0,
        icon: '💔',
        key: 'relationships',
        subtitle: this.getRelationshipVolatilitySubtitle(),
        highlight: this.getRelationshipVolatilityHighlight()
      },
      {
        name: 'Conflict Escalation',
        score: this.state.successMetrics.conflictEscalation?.successScore || 0,
        icon: '⚔️',
        key: 'conflicts',
        subtitle: this.getConflictEscalationSubtitle(),
        highlight: this.getConflictEscalationHighlight()
      },
      {
        name: 'World Building',
        score: this.state.successMetrics.worldBuildingDepth?.successScore || 0,
        icon: '🌍',
        key: 'world',
        subtitle: this.getWorldBuildingSubtitle(),
        highlight: this.getWorldBuildingHighlight()
      }
    ];

    return indicators.map(indicator => `
      <div class="indicator-card ${this.getScoreClass(indicator.score)} interactive-card" data-view="${indicator.key}">
        <div class="indicator-header">
          <div class="indicator-icon">${indicator.icon}</div>
          <div class="indicator-badge ${this.getScoreClass(indicator.score)}">${indicator.score}</div>
        </div>
        <div class="indicator-content">
          <div class="indicator-name">${indicator.name}</div>
          <div class="indicator-subtitle">${indicator.subtitle}</div>
          <div class="indicator-highlight">${indicator.highlight}</div>
        </div>
        <div class="indicator-bar">
          <div class="indicator-fill" style="width: ${indicator.score}%"></div>
        </div>
        <div class="indicator-action">
          <span class="action-arrow">→</span>
        </div>
      </div>
    `).join('');
  }

  private getPowerProgressionSubtitle(): string {
    const metrics = this.state.successMetrics?.powerProgression;
    if (!metrics) return '';
    return `Total Growth Events: ${metrics.totalLevels} • Avg Chapters Between: ${metrics.averageChaptersBetweenGrowth.toFixed(1)}`;
  }

  private getPowerProgressionHighlight(): string {
    const metrics = this.state.successMetrics?.powerProgression;
    if (!metrics) return '';
    if (metrics.successScore >= 80) return '🚀 Perfect progression pacing!';
    if (metrics.successScore >= 60) return '⭐ Strong growth trajectory';
    return '📈 Needs more frequent power-ups';
  }

  private getEmotionalIntensitySubtitle(): string {
    const metrics = this.state.successMetrics?.emotionalIntensity;
    if (!metrics) return '';
    return `Emotional Peaks (8+): ${metrics.totalPeaks} • Max Intensity: ${metrics.maxIntensity}/10`;
  }

  private getEmotionalIntensityHighlight(): string {
    const metrics = this.state.successMetrics?.emotionalIntensity;
    if (!metrics) return '';
    if (metrics.intensityTrend === 'escalating') return '🔥 Escalating emotional intensity!';
    if (metrics.totalPeaks >= 6) return '💥 High emotional engagement';
    return '📊 Moderate emotional impact';
  }

  private getCharacterArchetypesSubtitle(): string {
    const metrics = this.state.successMetrics?.characterArchetypes;
    if (!metrics) return '';
    return `Total Characters: ${metrics.totalCharacters} • Protagonist Agency: ${metrics.protagonistAgency}/10`;
  }

  private getCharacterArchetypesHighlight(): string {
    const metrics = this.state.successMetrics?.characterArchetypes;
    if (!metrics) return '';
    if (metrics.protagonistAgency >= 9) return '👑 Perfect protagonist dominance';
    if (metrics.balanceScore >= 70) return '⚖️ Well-balanced cast';
    return '🎭 Character roles need refinement';
  }

  private getSecretComplexitySubtitle(): string {
    const metrics = this.state.successMetrics?.secretComplexity;
    if (!metrics) return '';
    return `Secret Layers: ${metrics.secretLayers} • Secret Relationships: ${metrics.secretRelationships}`;
  }

  private getSecretComplexityHighlight(): string {
    const metrics = this.state.successMetrics?.secretComplexity;
    if (!metrics) return '';
    if (metrics.secretLayers >= 3) return '🕵️ Multi-layered mystery!';
    if (metrics.secretRelationships >= 2) return '🤫 Good secret complexity';
    return '🔍 Needs more mystery elements';
  }

  private getRelationshipVolatilitySubtitle(): string {
    const metrics = this.state.successMetrics?.relationshipVolatility;
    if (!metrics) return '';
    return `Volatile Relationships: ${metrics.volatileCount} • Drama Ratio: ${(metrics.volatilityRatio * 100).toFixed(0)}%`;
  }

  private getRelationshipVolatilityHighlight(): string {
    const metrics = this.state.successMetrics?.relationshipVolatility;
    if (!metrics) return '';
    if (metrics.volatilityRatio >= 0.3) return '🎭 High relationship drama!';
    if (metrics.dramaScore >= 60) return '💔 Good emotional tension';
    return '🤝 Relationships too stable';
  }

  private getConflictEscalationSubtitle(): string {
    const metrics = this.state.successMetrics?.conflictEscalation;
    if (!metrics) return '';
    return `Conflict Relationships: ${metrics.conflictRelationships} • Pattern: ${metrics.escalationPattern}`;
  }

  private getConflictEscalationHighlight(): string {
    const metrics = this.state.successMetrics?.conflictEscalation;
    if (!metrics) return '';
    if (metrics.escalationPattern === 'escalating') return '⚔️ Perfect conflict escalation!';
    if (metrics.conflictDiversity >= 4) return '🥊 Diverse conflict types';
    return '⚡ Needs more conflict variety';
  }

  private getWorldBuildingSubtitle(): string {
    const metrics = this.state.successMetrics?.worldBuildingDepth;
    if (!metrics) return '';
    return `Settings: ${metrics.settingCount} • Themes: ${metrics.themeCount} • Plot Arcs: ${metrics.plotArcCount}`;
  }

  private getWorldBuildingHighlight(): string {
    const metrics = this.state.successMetrics?.worldBuildingDepth;
    if (!metrics) return '';
    if (metrics.franchisePotential?.franchiseViability === 'High') return '🌟 Franchise potential!';
    if (metrics.settingCount >= 6) return '🏰 Rich world environment';
    return '🗺️ World needs expansion';
  }

  private renderNavigationTabs(): string {
    const tabs = [
      { id: 'overview', label: 'Success Overview', icon: '📊' },
      { id: 'power', label: 'Power Progression', icon: '⚡' },
      { id: 'emotional', label: 'Emotional Intensity', icon: '💥' },
      { id: 'characters', label: 'Character Archetypes', icon: '👥' },
      { id: 'secrets', label: 'Secret Complexity', icon: '🔐' },
      { id: 'relationships', label: 'Relationship Drama', icon: '💔' },
      { id: 'conflicts', label: 'Conflict Escalation', icon: '⚔️' },
      { id: 'world', label: 'World Building', icon: '🌍' }
    ];

    return `
      <div class="success-tabs">
        ${tabs.map(tab => `
          <button class="success-tab ${this.state.currentView === tab.id ? 'active' : ''}" 
                  data-view="${tab.id}">
            <span class="tab-icon">${tab.icon}</span>
            <span class="tab-label">${tab.label}</span>
          </button>
        `).join('')}
      </div>
    `;
  }

  private renderMainContent(): string {
    if (this.state.isLoading) {
      return '<div class="loading">🔄 Analyzing story success patterns...</div>';
    }

    switch (this.state.currentView) {
      case 'overview':
        return this.renderOverviewContent();
      case 'power':
        return this.renderPowerProgressionContent();
      case 'emotional':
        return this.renderEmotionalIntensityContent();
      case 'characters':
        return this.renderCharacterArchetypesContent();
      case 'secrets':
        return this.renderSecretComplexityContent();
      case 'relationships':
        return this.renderRelationshipVolatilityContent();
      case 'conflicts':
        return this.renderConflictEscalationContent();
      case 'world':
        return this.renderWorldBuildingContent();
      default:
        return this.renderOverviewContent();
    }
  }

  private renderOverviewContent(): string {
    if (!this.state.successMetrics) return '<div class="error">No success metrics available</div>';

    const overallScore = this.calculateOverallSuccessScore();
    
    return `
      <div class="success-content overview-content">
        <div class="overview-grid">
          <div class="success-breakdown">
            <h3>🎯 Success Factor Breakdown</h3>
            <div class="breakdown-chart">
              ${this.renderSuccessBreakdownChart()}
            </div>
          </div>
          
          <div class="market-prediction">
            <h3>📈 Market Success Prediction</h3>
            <div class="prediction-content">
              ${this.renderMarketPrediction(overallScore)}
            </div>
          </div>
          
          <div class="improvement-recommendations">
            <h3>💡 Improvement Recommendations</h3>
            <div class="recommendations-list">
              ${this.renderImprovementRecommendations()}
            </div>
          </div>
          
          <div class="success-patterns">
            <h3>🔍 Key Success Patterns Detected</h3>
            <div class="patterns-list">
              ${this.renderSuccessPatterns()}
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private renderSuccessBreakdownChart(): string {
    const metrics = this.state.successMetrics;
    const factors = [
      { name: 'Power Progression', score: metrics.powerProgression?.successScore || 0, color: '#ff6b6b' },
      { name: 'Emotional Intensity', score: metrics.emotionalIntensity?.successScore || 0, color: '#4ecdc4' },
      { name: 'Character Balance', score: metrics.characterArchetypes?.successScore || 0, color: '#45b7d1' },
      { name: 'Secret Complexity', score: metrics.secretComplexity?.successScore || 0, color: '#f9ca24' },
      { name: 'Relationship Drama', score: metrics.relationshipVolatility?.successScore || 0, color: '#f0932b' },
      { name: 'Conflict Escalation', score: metrics.conflictEscalation?.successScore || 0, color: '#eb4d4b' },
      { name: 'World Building', score: metrics.worldBuildingDepth?.successScore || 0, color: '#6c5ce7' }
    ];

    return factors.map(factor => `
      <div class="breakdown-item">
        <div class="factor-info">
          <span class="factor-name">${factor.name}</span>
          <span class="factor-score">${factor.score}/100</span>
        </div>
        <div class="factor-bar">
          <div class="factor-fill" style="width: ${factor.score}%; background-color: ${factor.color}"></div>
        </div>
      </div>
    `).join('');
  }

  private renderMarketPrediction(score: number): string {
    let prediction = '';
    let confidence = '';
    let marketComparison = '';

    if (score >= 80) {
      prediction = 'High probability of commercial success';
      confidence = '85-95%';
      marketComparison = 'Similar to top-performing fantasy/supernatural novels';
    } else if (score >= 60) {
      prediction = 'Good commercial potential with targeted marketing';
      confidence = '65-80%';
      marketComparison = 'Comparable to mid-tier successful novels';
    } else if (score >= 40) {
      prediction = 'Moderate potential, needs significant improvements';
      confidence = '35-60%';
      marketComparison = 'Below average market performance expected';
    } else {
      prediction = 'Low commercial viability in current form';
      confidence = '10-35%';
      marketComparison = 'Requires major restructuring for market success';
    }

    return `
      <div class="prediction-item">
        <div class="prediction-label">Market Success Probability:</div>
        <div class="prediction-value">${prediction}</div>
      </div>
      <div class="prediction-item">
        <div class="prediction-label">Confidence Level:</div>
        <div class="prediction-value">${confidence}</div>
      </div>
      <div class="prediction-item">
        <div class="prediction-label">Market Comparison:</div>
        <div class="prediction-value">${marketComparison}</div>
      </div>
    `;
  }

  private renderImprovementRecommendations(): string {
    if (!this.state.successMetrics) return '';

    const recommendations = [];
    const metrics = this.state.successMetrics;

    // Power Progression recommendations
    if (metrics.powerProgression?.successScore < 70) {
      recommendations.push({
        area: 'Power Progression',
        issue: 'Slow or inconsistent character growth',
        solution: 'Add more frequent ability unlocks and clearer progression milestones',
        priority: 'High'
      });
    }

    // Emotional Intensity recommendations
    if (metrics.emotionalIntensity?.successScore < 60) {
      recommendations.push({
        area: 'Emotional Intensity',
        issue: 'Insufficient emotional peaks',
        solution: 'Increase relationship conflicts and character stakes',
        priority: 'High'
      });
    }

    // Character Archetype recommendations
    if (metrics.characterArchetypes?.successScore < 60) {
      recommendations.push({
        area: 'Character Balance',
        issue: 'Poor character archetype distribution',
        solution: 'Strengthen supporting characters and add more diverse roles',
        priority: 'Medium'
      });
    }

    // Secret Complexity recommendations
    if (metrics.secretComplexity?.successScore < 50) {
      recommendations.push({
        area: 'Mystery & Secrets',
        issue: 'Insufficient secret layers',
        solution: 'Add more character secrets and information asymmetry',
        priority: 'Medium'
      });
    }

    return recommendations.slice(0, 4).map(rec => `
      <div class="recommendation-item priority-${rec.priority.toLowerCase()}">
        <div class="rec-header">
          <span class="rec-area">${rec.area}</span>
          <span class="rec-priority">${rec.priority} Priority</span>
        </div>
        <div class="rec-issue">Issue: ${rec.issue}</div>
        <div class="rec-solution">💡 ${rec.solution}</div>
      </div>
    `).join('');
  }

  private renderSuccessPatterns(): string {
    if (!this.state.successMetrics) return '';

    const patterns = [];
    const metrics = this.state.successMetrics;

    // Analyze patterns
    if (metrics.powerProgression?.growthTrajectory === 'overall|positive') {
      patterns.push('✅ Strong protagonist growth trajectory detected');
    }

    if (metrics.emotionalIntensity?.intensityTrend === 'escalating') {
      patterns.push('✅ Escalating emotional intensity creates addictive reading');
    }

    if (metrics.relationshipVolatility?.volatilityRatio > 0.3) {
      patterns.push('✅ High relationship drama drives engagement');
    }

    if (metrics.secretComplexity?.secretLayers >= 3) {
      patterns.push('✅ Multi-layered secrets create binge-reading behavior');
    }

    if (metrics.conflictEscalation?.escalationPattern === 'escalating') {
      patterns.push('✅ Escalating conflicts maintain tension');
    }

    if (patterns.length === 0) {
      patterns.push('⚠️ No strong success patterns detected');
    }

    return patterns.map(pattern => `
      <div class="pattern-item">${pattern}</div>
    `).join('');
  }

  private renderPowerProgressionContent(): string {
    const metrics = this.state.successMetrics?.powerProgression;
    if (!metrics) return '<div class="error">No power progression data available</div>';

    return `
      <div class="success-content power-content">
        <div class="content-header">
          <h2>⚡ Power Progression Analysis</h2>
          <div class="score-badge ${this.getScoreClass(metrics.successScore)}">
            ${metrics.successScore}/100
          </div>
        </div>

        <div class="power-grid">
          <div class="progression-stats">
            <h3>📊 Progression Statistics</h3>
            <div class="stat-grid">
              <div class="stat-item">
                <div class="stat-label">Total Growth Events</div>
                <div class="stat-value">${metrics.totalLevels}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">Progression Velocity</div>
                <div class="stat-value">${(metrics.progressionVelocity * 100).toFixed(1)}%</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">Chapters Between Growth</div>
                <div class="stat-value">${metrics.averageChaptersBetweenGrowth.toFixed(1)}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">Growth Trajectory</div>
                <div class="stat-value">${metrics.growthTrajectory}</div>
              </div>
            </div>
          </div>

          <div class="growth-events-details">
            <h3>🎯 Major Growth Events</h3>
            <div class="growth-events-list">
              ${this.renderGrowthEventsDetails()}
            </div>
          </div>

          <div class="progression-analysis">
            <h3>🎯 Success Analysis</h3>
            <div class="analysis-content">
              ${this.renderProgressionAnalysis(metrics)}
            </div>
          </div>

          <div class="progression-timeline">
            <h3>📈 Character Evolution</h3>
            <div class="evolution-summary">
              <div class="evolution-state">
                <h4>Final State:</h4>
                <p>${metrics.finalState}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private renderGrowthEventsDetails(): string {
    const protagonist = storyIntelligence.getProtagonist();
    if (!protagonist?.character_evolution?.major_turning_points) {
      return '<div class="no-data">No detailed growth events available</div>';
    }

    const turningPoints = protagonist.character_evolution.major_turning_points;

    return turningPoints.map((point, index) => `
      <div class="growth-event-card">
        <div class="event-header">
          <div class="event-number">Event ${index + 1}</div>
          <div class="event-chapter">Chapter ${point.chapter}</div>
        </div>
        <div class="event-content">
          <div class="event-description">${point.event}</div>
          <div class="event-impact">
            <span class="impact-label">Impact:</span>
            <span class="impact-text">${point.impact}</span>
          </div>
        </div>
      </div>
    `).join('');
  }

  private renderProgressionAnalysis(metrics: any): string {
    let analysis = '';

    if (metrics.successScore >= 80) {
      analysis = '🚀 Excellent progression pacing! The character growth follows optimal patterns that keep readers engaged.';
    } else if (metrics.successScore >= 60) {
      analysis = '⭐ Good progression with room for improvement. Consider adding more frequent minor victories.';
    } else if (metrics.successScore >= 40) {
      analysis = '📚 Moderate progression. The character growth could be more consistent and impactful.';
    } else {
      analysis = '⚠️ Weak progression pattern. Character growth is too slow or inconsistent for market success.';
    }

    const optimalRange = 'Optimal: 1 major growth event every 5-7 chapters';
    const currentPacing = `Current: 1 event every ${metrics.averageChaptersBetweenGrowth.toFixed(1)} chapters`;

    return `
      <div class="analysis-item">
        <div class="analysis-text">${analysis}</div>
      </div>
      <div class="analysis-item">
        <div class="analysis-label">Pacing Analysis:</div>
        <div class="analysis-detail">${optimalRange}</div>
        <div class="analysis-detail">${currentPacing}</div>
      </div>
    `;
  }

  private renderEmotionalIntensityContent(): string {
    const metrics = this.state.successMetrics?.emotionalIntensity;
    if (!metrics) return '<div class="error">No emotional intensity data available</div>';

    return `
      <div class="success-content emotional-content">
        <div class="content-header">
          <h2>💥 Emotional Intensity Analysis</h2>
          <div class="score-badge ${this.getScoreClass(metrics.successScore)}">
            ${metrics.successScore}/100
          </div>
        </div>

        <div class="emotional-grid">
          <div class="intensity-stats">
            <h3>📊 Intensity Metrics</h3>
            <div class="stat-grid">
              <div class="stat-item">
                <div class="stat-label">Emotional Peaks (8+)</div>
                <div class="stat-value">${metrics.totalPeaks}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">Maximum Intensity</div>
                <div class="stat-value">${metrics.maxIntensity}/10</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">Average Intensity</div>
                <div class="stat-value">${metrics.averageIntensity.toFixed(1)}/10</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">Peak Frequency</div>
                <div class="stat-value">${(metrics.peakFrequency * 100).toFixed(1)}%</div>
              </div>
            </div>
          </div>

          <div class="intensity-analysis">
            <h3>🎯 Engagement Analysis</h3>
            <div class="analysis-content">
              ${this.renderEmotionalAnalysis(metrics)}
            </div>
          </div>

          <div class="intensity-trends">
            <h3>📈 Intensity Trends</h3>
            <div class="trend-content">
              <div class="trend-item">
                <span class="trend-label">Trend Pattern:</span>
                <span class="trend-value ${metrics.intensityTrend}">${metrics.intensityTrend}</span>
              </div>
              <div class="trend-item">
                <span class="trend-label">Optimal Pacing:</span>
                <span class="trend-value ${metrics.optimalPacing ? 'optimal' : 'suboptimal'}">
                  ${metrics.optimalPacing ? 'Yes' : 'No'}
                </span>
              </div>
            </div>
          </div>

          <div class="emotional-peaks-details">
            <h3>🔥 High-Intensity Moments</h3>
            <div class="peaks-list">
              ${this.renderEmotionalPeaksDetails()}
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private renderEmotionalPeaksDetails(): string {
    const relationships = storyIntelligence.getAllRelationships();
    const allInteractions = relationships.flatMap(rel => rel.interaction_timeline || []);
    const intensityPeaks = allInteractions
      .filter(interaction => interaction.emotional_intensity >= 8)
      .sort((a, b) => b.emotional_intensity - a.emotional_intensity)
      .slice(0, 10); // Show top 10 peaks

    if (intensityPeaks.length === 0) {
      return '<div class="no-data">No high-intensity emotional peaks found</div>';
    }

    return intensityPeaks.map((peak, index) => `
      <div class="peak-card">
        <div class="peak-header">
          <div class="peak-rank">#${index + 1}</div>
          <div class="peak-intensity">${peak.emotional_intensity}/10</div>
          <div class="peak-chapter">Chapter ${peak.chapter}</div>
        </div>
        <div class="peak-content">
          <div class="peak-type">${peak.interaction_type}</div>
          <div class="peak-description">${peak.interaction_summary}</div>
          <div class="peak-significance">
            <span class="significance-label">Plot Significance:</span>
            <span class="significance-value">${peak.plot_significance}/10</span>
          </div>
        </div>
      </div>
    `).join('');
  }

  private renderEmotionalAnalysis(metrics: any): string {
    let analysis = '';

    if (metrics.successScore >= 80) {
      analysis = '🚀 Excellent emotional pacing! Perfect balance of intensity peaks creates addictive reading.';
    } else if (metrics.successScore >= 60) {
      analysis = '⭐ Good emotional engagement with room for more dramatic moments.';
    } else if (metrics.successScore >= 40) {
      analysis = '📚 Moderate emotional impact. Consider adding more high-stakes moments.';
    } else {
      analysis = '⚠️ Low emotional engagement. Story needs more dramatic tension and character stakes.';
    }

    return `
      <div class="analysis-item">
        <div class="analysis-text">${analysis}</div>
      </div>
      <div class="analysis-item">
        <div class="analysis-label">Optimal Pattern:</div>
        <div class="analysis-detail">1 major emotional peak every 8-12 chapters</div>
        <div class="analysis-detail">Current: ${metrics.totalPeaks} peaks detected</div>
      </div>
    `;
  }

  private renderCharacterArchetypesContent(): string {
    const metrics = this.state.successMetrics?.characterArchetypes;
    if (!metrics) return '<div class="error">No character archetype data available</div>';

    return `
      <div class="success-content character-content">
        <div class="content-header">
          <h2>👥 Character Archetype Analysis</h2>
          <div class="score-badge ${this.getScoreClass(metrics.successScore)}">
            ${metrics.successScore}/100
          </div>
        </div>

        <div class="character-grid">
          <div class="archetype-distribution">
            <h3>📊 Archetype Distribution</h3>
            <div class="distribution-chart">
              ${this.renderArchetypeChart(metrics.archetypeDistribution)}
            </div>
          </div>

          <div class="character-stats">
            <h3>📈 Character Metrics</h3>
            <div class="stat-grid">
              <div class="stat-item">
                <div class="stat-label">Total Characters</div>
                <div class="stat-value">${metrics.totalCharacters}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">Protagonist Agency</div>
                <div class="stat-value">${metrics.protagonistAgency}/10</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">Supporting Characters</div>
                <div class="stat-value">${metrics.supportingCharacters}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">Balance Score</div>
                <div class="stat-value">${metrics.balanceScore}/100</div>
              </div>
            </div>
          </div>

          <div class="character-details">
            <h3>🎭 Character Breakdown</h3>
            <div class="character-breakdown-list">
              ${this.renderCharacterBreakdown()}
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private renderCharacterBreakdown(): string {
    const characters = storyIntelligence.getAllCharacters()
      .sort((a, b) => b.overall_importance - a.overall_importance)
      .slice(0, 8); // Show top 8 characters

    if (characters.length === 0) {
      return '<div class="no-data">No character data available</div>';
    }

    return characters.map((char, index) => `
      <div class="character-breakdown-card">
        <div class="character-header">
          <div class="character-rank">#${index + 1}</div>
          <div class="character-name">${char.character_name}</div>
          <div class="character-importance">${char.overall_importance}/10</div>
        </div>
        <div class="character-details">
          <div class="character-archetype">${char.character_archetype}</div>
          <div class="character-presence">
            <span class="presence-label">Chapters Present:</span>
            <span class="presence-value">${char.total_chapters_present}</span>
          </div>
          <div class="character-motivations">
            <span class="motivations-label">Core Motivations:</span>
            <div class="motivations-list">
              ${(char.core_motivations || []).slice(0, 2).map(motivation =>
                `<span class="motivation-item">${motivation}</span>`
              ).join('')}
            </div>
          </div>
        </div>
      </div>
    `).join('');
  }

  private renderArchetypeChart(distribution: Record<string, number>): string {
    const total = Object.values(distribution).reduce((sum, count) => sum + count, 0);

    return Object.entries(distribution).map(([archetype, count]) => {
      const percentage = (count / total) * 100;
      return `
        <div class="archetype-item">
          <div class="archetype-info">
            <span class="archetype-name">${archetype}</span>
            <span class="archetype-count">${count} (${percentage.toFixed(1)}%)</span>
          </div>
          <div class="archetype-bar">
            <div class="archetype-fill" style="width: ${percentage}%"></div>
          </div>
        </div>
      `;
    }).join('');
  }

  private renderSecretComplexityContent(): string {
    const metrics = this.state.successMetrics?.secretComplexity;
    if (!metrics) return '<div class="error">No secret complexity data available</div>';

    return `
      <div class="success-content secret-content">
        <div class="content-header">
          <h2>🔐 Secret Complexity Analysis</h2>
          <div class="score-badge ${this.getScoreClass(metrics.successScore)}">
            ${metrics.successScore}/100
          </div>
        </div>

        <div class="secret-grid">
          <div class="secret-stats">
            <h3>📊 Mystery Metrics</h3>
            <div class="stat-grid">
              <div class="stat-item">
                <div class="stat-label">Secret Relationships</div>
                <div class="stat-value">${metrics.secretRelationships}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">Secret Keepers</div>
                <div class="stat-value">${metrics.secretKeepers}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">Secret Layers</div>
                <div class="stat-value">${metrics.secretLayers}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">Information Asymmetry</div>
                <div class="stat-value">${metrics.informationAsymmetry}</div>
              </div>
            </div>
          </div>

          <div class="revelation-timeline">
            <h3>📈 Revelation Pattern</h3>
            <div class="timeline-content">
              <div class="timeline-stat">
                <span class="timeline-label">Total Revelations:</span>
                <span class="timeline-value">${metrics.totalRevelations}</span>
              </div>
              <div class="timeline-stat">
                <span class="timeline-label">Average Chapter:</span>
                <span class="timeline-value">${metrics.averageRevelationChapter.toFixed(1)}</span>
              </div>
            </div>
          </div>

          <div class="secret-details">
            <h3>🕵️ Secret Breakdown</h3>
            <div class="secret-breakdown-list">
              ${this.renderSecretBreakdown()}
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private renderSecretBreakdown(): string {
    const relationships = storyIntelligence.getAllRelationships();
    const secretRelationships = relationships.filter(rel =>
      rel.relationship_classification?.toLowerCase().includes('secret') ||
      rel.relationship_summary?.toLowerCase().includes('secret') ||
      rel.relationship_summary?.toLowerCase().includes('hidden')
    );

    if (secretRelationships.length === 0) {
      return '<div class="no-data">No secret relationships detected</div>';
    }

    return secretRelationships.slice(0, 6).map((rel, index) => {
      const charA = storyIntelligence.getCharacter(rel.character_a_id);
      const charB = storyIntelligence.getCharacter(rel.character_b_id);

      return `
        <div class="secret-breakdown-card">
          <div class="secret-header">
            <div class="secret-rank">#${index + 1}</div>
            <div class="secret-characters">
              ${charA?.character_name || rel.character_a_id} ↔ ${charB?.character_name || rel.character_b_id}
            </div>
            <div class="secret-strength">${rel.overall_strength}/10</div>
          </div>
          <div class="secret-content">
            <div class="secret-type">${rel.relationship_classification}</div>
            <div class="secret-description">${rel.relationship_summary}</div>
            <div class="secret-implications">
              <span class="implications-label">Future Impact:</span>
              <span class="implications-text">${rel.future_implications || 'Unknown'}</span>
            </div>
          </div>
        </div>
      `;
    }).join('');
  }

  private renderRelationshipVolatilityContent(): string {
    const metrics = this.state.successMetrics?.relationshipVolatility;
    if (!metrics) return '<div class="error">No relationship volatility data available</div>';

    return `
      <div class="success-content relationship-content">
        <div class="content-header">
          <h2>💔 Relationship Drama Analysis</h2>
          <div class="score-badge ${this.getScoreClass(metrics.successScore)}">
            ${metrics.successScore}/100
          </div>
        </div>

        <div class="relationship-grid">
          <div class="volatility-stats">
            <h3>📊 Drama Metrics</h3>
            <div class="stat-grid">
              <div class="stat-item">
                <div class="stat-label">Volatile Relationships</div>
                <div class="stat-value">${metrics.volatileCount}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">Volatility Ratio</div>
                <div class="stat-value">${(metrics.volatilityRatio * 100).toFixed(1)}%</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">Average Volatility</div>
                <div class="stat-value">${metrics.averageVolatility.toFixed(1)}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">Drama Score</div>
                <div class="stat-value">${metrics.dramaScore}/100</div>
              </div>
            </div>
          </div>

          <div class="relationship-details">
            <h3>💥 Most Volatile Relationships</h3>
            <div class="relationship-breakdown-list">
              ${this.renderRelationshipBreakdown()}
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private renderRelationshipBreakdown(): string {
    const relationships = storyIntelligence.getAllRelationships();
    const volatileRelationships = relationships
      .filter(rel => rel.relationship_stability === 'volatile' || rel.relationship_stability === 'unstable')
      .sort((a, b) => {
        const aChanges = (a.relationship_evolution?.key_developments || []).length;
        const bChanges = (b.relationship_evolution?.key_developments || []).length;
        return bChanges - aChanges;
      })
      .slice(0, 6);

    if (volatileRelationships.length === 0) {
      return '<div class="no-data">No volatile relationships detected</div>';
    }

    return volatileRelationships.map((rel, index) => {
      const charA = storyIntelligence.getCharacter(rel.character_a_id);
      const charB = storyIntelligence.getCharacter(rel.character_b_id);
      const developments = rel.relationship_evolution?.key_developments || [];
      const totalChanges = developments.reduce((sum, dev) => sum + Math.abs(dev.relationship_change || 0), 0);

      return `
        <div class="relationship-breakdown-card">
          <div class="relationship-header">
            <div class="relationship-rank">#${index + 1}</div>
            <div class="relationship-characters">
              ${charA?.character_name || rel.character_a_id} ↔ ${charB?.character_name || rel.character_b_id}
            </div>
            <div class="relationship-volatility">${totalChanges.toFixed(1)}</div>
          </div>
          <div class="relationship-content">
            <div class="relationship-type">${rel.relationship_classification}</div>
            <div class="relationship-description">${rel.relationship_summary}</div>
            <div class="relationship-stability">
              <span class="stability-label">Stability:</span>
              <span class="stability-value ${rel.relationship_stability}">${rel.relationship_stability}</span>
            </div>
            <div class="relationship-changes">
              <span class="changes-label">Major Changes:</span>
              <span class="changes-value">${developments.length}</span>
            </div>
          </div>
        </div>
      `;
    }).join('');
  }

  private renderConflictEscalationContent(): string {
    const metrics = this.state.successMetrics?.conflictEscalation;
    if (!metrics) return '<div class="error">No conflict escalation data available</div>';

    return `
      <div class="success-content conflict-content">
        <div class="content-header">
          <h2>⚔️ Conflict Escalation Analysis</h2>
          <div class="score-badge ${this.getScoreClass(metrics.successScore)}">
            ${metrics.successScore}/100
          </div>
        </div>

        <div class="conflict-grid">
          <div class="conflict-stats">
            <h3>📊 Conflict Metrics</h3>
            <div class="stat-grid">
              <div class="stat-item">
                <div class="stat-label">Conflict Relationships</div>
                <div class="stat-value">${metrics.conflictRelationships}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">Conflict Diversity</div>
                <div class="stat-value">${metrics.conflictDiversity}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">Escalation Pattern</div>
                <div class="stat-value">${metrics.escalationPattern}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">Conflict Intensity</div>
                <div class="stat-value">${metrics.conflictIntensity.toFixed(1)}/10</div>
              </div>
            </div>
          </div>

          <div class="conflict-details">
            <h3>⚔️ Major Conflicts</h3>
            <div class="conflict-breakdown-list">
              ${this.renderConflictBreakdown()}
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private renderConflictBreakdown(): string {
    const relationships = storyIntelligence.getAllRelationships();
    const conflictRelationships = relationships
      .filter(rel => {
        const interactions = rel.interaction_timeline || [];
        return interactions.some(interaction =>
          interaction.interaction_type?.toLowerCase().includes('conflict') ||
          interaction.interaction_type?.toLowerCase().includes('fight') ||
          interaction.interaction_type?.toLowerCase().includes('argument') ||
          interaction.emotional_intensity >= 7
        );
      })
      .sort((a, b) => {
        const aMaxIntensity = Math.max(...(a.interaction_timeline || []).map(i => i.emotional_intensity || 0));
        const bMaxIntensity = Math.max(...(b.interaction_timeline || []).map(i => i.emotional_intensity || 0));
        return bMaxIntensity - aMaxIntensity;
      })
      .slice(0, 6);

    if (conflictRelationships.length === 0) {
      return '<div class="no-data">No major conflicts detected</div>';
    }

    return conflictRelationships.map((rel, index) => {
      const charA = storyIntelligence.getCharacter(rel.character_a_id);
      const charB = storyIntelligence.getCharacter(rel.character_b_id);
      const conflictInteractions = (rel.interaction_timeline || []).filter(i =>
        i.interaction_type?.toLowerCase().includes('conflict') || i.emotional_intensity >= 7
      );
      const maxIntensity = Math.max(...conflictInteractions.map(i => i.emotional_intensity || 0));

      return `
        <div class="conflict-breakdown-card">
          <div class="conflict-header">
            <div class="conflict-rank">#${index + 1}</div>
            <div class="conflict-characters">
              ${charA?.character_name || rel.character_a_id} ⚔️ ${charB?.character_name || rel.character_b_id}
            </div>
            <div class="conflict-intensity">${maxIntensity}/10</div>
          </div>
          <div class="conflict-content">
            <div class="conflict-type">${rel.relationship_classification}</div>
            <div class="conflict-description">${rel.relationship_summary}</div>
            <div class="conflict-interactions">
              <span class="interactions-label">Conflict Events:</span>
              <span class="interactions-value">${conflictInteractions.length}</span>
            </div>
            <div class="conflict-influence">
              <span class="influence-label">Mutual Influence:</span>
              <span class="influence-text">${rel.mutual_influence || 'Unknown'}</span>
            </div>
          </div>
        </div>
      `;
    }).join('');
  }

  private renderWorldBuildingContent(): string {
    const metrics = this.state.successMetrics?.worldBuildingDepth;
    if (!metrics) return '<div class="error">No world building data available</div>';

    return `
      <div class="success-content world-content">
        <div class="content-header">
          <h2>🌍 World Building Analysis</h2>
          <div class="score-badge ${this.getScoreClass(metrics.successScore)}">
            ${metrics.successScore}/100
          </div>
        </div>

        <div class="world-comprehensive-grid">
          <div class="world-overview-section">
            <h3>🎯 World Building Success Factors</h3>
            <div class="success-factors-grid">
              ${this.renderWorldSuccessFactors(metrics)}
            </div>
          </div>

          <div class="settings-analysis-section">
            <h3>🏰 Environment Analysis</h3>
            <div class="settings-detailed-view">
              ${this.renderSettingsAnalysis(metrics)}
            </div>
          </div>

          <div class="themes-analysis-section">
            <h3>🎭 Thematic Depth Analysis</h3>
            <div class="themes-detailed-view">
              ${this.renderThemesAnalysis(metrics)}
            </div>
          </div>

          <div class="plot-arcs-section">
            <h3>📚 Narrative Structure Analysis</h3>
            <div class="plot-arcs-detailed-view">
              ${this.renderPlotArcsAnalysis(metrics)}
            </div>
          </div>

          <div class="immersion-factors-section">
            <h3>🌟 Immersion & Franchise Potential</h3>
            <div class="immersion-analysis">
              ${this.renderImmersionAnalysis(metrics)}
            </div>
          </div>

          <div class="narrative-progression-section">
            <h3>📈 Narrative Progression</h3>
            <div class="progression-analysis">
              ${this.renderNarrativeProgression(metrics)}
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private renderWorldSuccessFactors(metrics: any): string {
    const factors = [
      {
        name: 'Environmental Richness',
        score: metrics.immersionFactors?.environmentalRichness || 0,
        description: 'Variety and depth of story settings',
        icon: '🏞️'
      },
      {
        name: 'Thematic Depth',
        score: metrics.immersionFactors?.thematicDepth || 0,
        description: 'Complexity and relevance of themes',
        icon: '🎭'
      },
      {
        name: 'Narrative Complexity',
        score: metrics.immersionFactors?.narrativeComplexity || 0,
        description: 'Multi-layered plot development',
        icon: '📚'
      },
      {
        name: 'Franchise Potential',
        score: metrics.franchisePotential?.totalScore || 0,
        description: 'Expandability for sequels/series',
        icon: '🚀'
      }
    ];

    return factors.map(factor => `
      <div class="success-factor-card ${this.getScoreClass(factor.score)}">
        <div class="factor-header">
          <span class="factor-icon">${factor.icon}</span>
          <span class="factor-score">${factor.score.toFixed(0)}/100</span>
        </div>
        <div class="factor-content">
          <h4>${factor.name}</h4>
          <p>${factor.description}</p>
        </div>
        <div class="factor-bar">
          <div class="factor-fill" style="width: ${factor.score}%"></div>
        </div>
      </div>
    `).join('');
  }

  private renderSettingsAnalysis(metrics: any): string {
    const settingAnalysis = metrics.settingAnalysis;
    if (!settingAnalysis) return '<div class="no-data">No setting analysis available</div>';

    return `
      <div class="settings-overview">
        <div class="settings-summary">
          <div class="summary-stat">
            <span class="stat-label">Total Settings:</span>
            <span class="stat-value">${metrics.settingCount}</span>
          </div>
          <div class="summary-stat">
            <span class="stat-label">Dominant Category:</span>
            <span class="stat-value">${settingAnalysis.dominantCategory}</span>
          </div>
          <div class="summary-stat">
            <span class="stat-label">Atmosphere Variety:</span>
            <span class="stat-value">${settingAnalysis.atmosphereVariety?.toFixed(1)}/10</span>
          </div>
        </div>

        <div class="settings-categories">
          <h4>📍 Settings by Category</h4>
          <div class="categories-grid">
            ${Object.entries(settingAnalysis.categorizedSettings).map(([category, settings]) => `
              <div class="category-card">
                <div class="category-header">
                  <h5>${category}</h5>
                  <span class="category-count">${(settings as string[]).length}</span>
                </div>
                <div class="category-settings">
                  ${(settings as string[]).map(setting => `
                    <div class="setting-tag">${setting}</div>
                  `).join('')}
                </div>
              </div>
            `).join('')}
          </div>
        </div>

        <div class="settings-details">
          <h4>🎯 Detailed Setting Analysis</h4>
          <div class="settings-list">
            ${settingAnalysis.settingDetails?.map((setting: any) => `
              <div class="setting-detail-card">
                <div class="setting-name">${setting.name}</div>
                <div class="setting-metrics">
                  <div class="setting-metric">
                    <span class="metric-label">Category:</span>
                    <span class="metric-value">${setting.category}</span>
                  </div>
                  <div class="setting-metric">
                    <span class="metric-label">Atmosphere:</span>
                    <span class="metric-value">${setting.atmosphereScore}/10</span>
                  </div>
                  <div class="setting-metric">
                    <span class="metric-label">Conflict Potential:</span>
                    <span class="metric-value">${setting.conflictPotential}/10</span>
                  </div>
                </div>
              </div>
            `).join('') || ''}
          </div>
        </div>
      </div>
    `;
  }

  private renderThemesAnalysis(metrics: any): string {
    const themeAnalysis = metrics.themeAnalysis;
    if (!themeAnalysis) return '<div class="no-data">No theme analysis available</div>';

    return `
      <div class="themes-overview">
        <div class="themes-summary">
          <div class="summary-stat">
            <span class="stat-label">Total Themes:</span>
            <span class="stat-value">${metrics.themeCount}</span>
          </div>
          <div class="summary-stat">
            <span class="stat-label">Dominant Category:</span>
            <span class="stat-value">${themeAnalysis.dominantCategory}</span>
          </div>
          <div class="summary-stat">
            <span class="stat-label">Avg Complexity:</span>
            <span class="stat-value">${themeAnalysis.averageComplexity?.toFixed(1)}/10</span>
          </div>
          <div class="summary-stat">
            <span class="stat-label">Market Appeal:</span>
            <span class="stat-value">${themeAnalysis.averageMarketAppeal?.toFixed(1)}/10</span>
          </div>
        </div>

        <div class="themes-categories">
          <h4>🎭 Themes by Category</h4>
          <div class="theme-categories-grid">
            ${Object.entries(themeAnalysis.categorizedThemes).map(([category, themes]) => `
              <div class="theme-category-card">
                <div class="theme-category-header">
                  <h5>${category}</h5>
                  <span class="theme-category-count">${(themes as string[]).length}</span>
                </div>
                <div class="theme-category-items">
                  ${(themes as string[]).map(theme => `
                    <div class="theme-tag">${theme}</div>
                  `).join('')}
                </div>
              </div>
            `).join('')}
          </div>
        </div>

        <div class="themes-details">
          <h4>📊 Theme Performance Analysis</h4>
          <div class="themes-performance-list">
            ${themeAnalysis.themeDetails?.map((theme: any) => `
              <div class="theme-performance-card">
                <div class="theme-name">${theme.name}</div>
                <div class="theme-performance-metrics">
                  <div class="performance-metric">
                    <span class="metric-label">Category:</span>
                    <span class="metric-value">${theme.category}</span>
                  </div>
                  <div class="performance-metric">
                    <span class="metric-label">Complexity:</span>
                    <div class="metric-bar">
                      <div class="metric-fill" style="width: ${theme.complexity * 10}%"></div>
                      <span class="metric-score">${theme.complexity}/10</span>
                    </div>
                  </div>
                  <div class="performance-metric">
                    <span class="metric-label">Market Appeal:</span>
                    <div class="metric-bar">
                      <div class="metric-fill" style="width: ${theme.marketAppeal * 10}%"></div>
                      <span class="metric-score">${theme.marketAppeal.toFixed(1)}/10</span>
                    </div>
                  </div>
                </div>
              </div>
            `).join('') || ''}
          </div>
        </div>
      </div>
    `;
  }

  private renderPlotArcsAnalysis(metrics: any): string {
    const plotArcs = metrics.plotArcs || [];
    const plotArcAnalysis = metrics.plotArcAnalysis || [];

    return `
      <div class="plot-arcs-overview">
        <div class="plot-summary">
          <div class="summary-stat">
            <span class="stat-label">Total Plot Arcs:</span>
            <span class="stat-value">${metrics.plotArcCount}</span>
          </div>
          <div class="summary-stat">
            <span class="stat-label">Narrative Complexity:</span>
            <span class="stat-value">${metrics.narrativeComplexity}/10</span>
          </div>
        </div>

        <div class="plot-arcs-detailed">
          <h4>📖 Plot Arc Breakdown</h4>
          <div class="plot-arcs-list">
            ${plotArcAnalysis.map((arc: any, index: number) => `
              <div class="plot-arc-card">
                <div class="arc-header">
                  <div class="arc-number">Arc ${index + 1}</div>
                  <div class="arc-type-badge">${arc.arcType}</div>
                </div>
                <div class="arc-description">${arc.description}</div>
                <div class="arc-metrics">
                  <div class="arc-metric">
                    <span class="metric-label">Complexity:</span>
                    <div class="metric-bar">
                      <div class="metric-fill" style="width: ${arc.complexity * 10}%"></div>
                      <span class="metric-score">${arc.complexity.toFixed(1)}/10</span>
                    </div>
                  </div>
                  <div class="arc-metric">
                    <span class="metric-label">Tension:</span>
                    <div class="metric-bar">
                      <div class="metric-fill" style="width: ${arc.tension * 10}%"></div>
                      <span class="metric-score">${arc.tension.toFixed(1)}/10</span>
                    </div>
                  </div>
                  ${arc.characterFocus?.length > 0 ? `
                    <div class="arc-characters">
                      <span class="metric-label">Key Characters:</span>
                      <div class="character-tags">
                        ${arc.characterFocus.map((char: string) => `
                          <span class="character-tag">${char}</span>
                        `).join('')}
                      </div>
                    </div>
                  ` : ''}
                </div>
              </div>
            `).join('')}
          </div>
        </div>
      </div>
    `;
  }

  private renderImmersionAnalysis(metrics: any): string {
    const immersion = metrics.immersionFactors || {};
    const franchise = metrics.franchisePotential || {};

    return `
      <div class="immersion-analysis-content">
        <div class="immersion-overview">
          <h4>🌟 Immersion Factors</h4>
          <div class="immersion-metrics">
            <div class="immersion-metric">
              <div class="metric-header">
                <span class="metric-icon">🏞️</span>
                <span class="metric-name">Environmental Richness</span>
              </div>
              <div class="metric-score-bar">
                <div class="metric-fill" style="width: ${immersion.environmentalRichness || 0}%"></div>
                <span class="metric-value">${(immersion.environmentalRichness || 0).toFixed(0)}/100</span>
              </div>
            </div>
            <div class="immersion-metric">
              <div class="metric-header">
                <span class="metric-icon">🎭</span>
                <span class="metric-name">Thematic Depth</span>
              </div>
              <div class="metric-score-bar">
                <div class="metric-fill" style="width: ${immersion.thematicDepth || 0}%"></div>
                <span class="metric-value">${(immersion.thematicDepth || 0).toFixed(0)}/100</span>
              </div>
            </div>
            <div class="immersion-metric">
              <div class="metric-header">
                <span class="metric-icon">📚</span>
                <span class="metric-name">Narrative Complexity</span>
              </div>
              <div class="metric-score-bar">
                <div class="metric-fill" style="width: ${immersion.narrativeComplexity || 0}%"></div>
                <span class="metric-value">${(immersion.narrativeComplexity || 0).toFixed(0)}/100</span>
              </div>
            </div>
          </div>
          <div class="overall-immersion">
            <div class="overall-score ${this.getScoreClass(immersion.overallImmersion || 0)}">
              <span class="score-label">Overall Immersion Score</span>
              <span class="score-value">${(immersion.overallImmersion || 0).toFixed(0)}/100</span>
            </div>
          </div>
        </div>

        <div class="franchise-potential">
          <h4>🚀 Franchise Potential Analysis</h4>
          <div class="franchise-metrics">
            <div class="franchise-metric">
              <span class="franchise-label">World Expandability:</span>
              <span class="franchise-value">${franchise.worldExpandability || 0}/25</span>
            </div>
            <div class="franchise-metric">
              <span class="franchise-label">Thematic Versatility:</span>
              <span class="franchise-value">${franchise.thematicVersatility || 0}/25</span>
            </div>
            <div class="franchise-metric">
              <span class="franchise-label">Narrative Scalability:</span>
              <span class="franchise-value">${franchise.narrativeScalability || 0}/25</span>
            </div>
            <div class="franchise-metric">
              <span class="franchise-label">Character Development Potential:</span>
              <span class="franchise-value">${franchise.characterDevelopmentPotential || 0}/25</span>
            </div>
          </div>
          <div class="franchise-verdict">
            <div class="verdict-score ${this.getScoreClass(franchise.totalScore || 0)}">
              <span class="verdict-label">Franchise Viability:</span>
              <span class="verdict-value">${franchise.franchiseViability || 'Unknown'}</span>
              <span class="verdict-score-value">${franchise.totalScore || 0}/100</span>
            </div>
            <div class="verdict-explanation">
              ${this.getFranchiseExplanation(franchise.franchiseViability)}
            </div>
          </div>
        </div>
      </div>
    `;
  }

  private getFranchiseExplanation(viability: string): string {
    switch (viability) {
      case 'High':
        return '🌟 Excellent potential for sequels, spin-offs, and expanded universe content. Rich world and characters provide multiple story opportunities.';
      case 'Medium':
        return '⭐ Good foundation for franchise development with some areas needing enhancement. Consider expanding world-building elements.';
      case 'Low':
        return '📚 Limited franchise potential in current form. Focus on deepening world-building and character development for future expansion.';
      default:
        return '🔍 Franchise potential analysis unavailable.';
    }
  }

  private renderNarrativeProgression(metrics: any): string {
    const progression = metrics.narrativeProgression || '';

    return `
      <div class="narrative-progression-content">
        <div class="progression-overview">
          <h4>📈 Story Progression Analysis</h4>
          <div class="progression-description">
            <p>${progression}</p>
          </div>
        </div>

        <div class="progression-insights">
          <h4>🎯 Key Progression Insights</h4>
          <div class="insights-grid">
            ${this.renderProgressionInsights(metrics)}
          </div>
        </div>
      </div>
    `;
  }

  private renderProgressionInsights(metrics: any): string {
    const insights = [
      {
        title: 'Character Arc Strength',
        value: this.calculateCharacterArcStrength(metrics),
        description: 'How well the protagonist evolves throughout the story',
        icon: '👤'
      },
      {
        title: 'World Complexity Growth',
        value: this.calculateWorldComplexityGrowth(metrics),
        description: 'How the world expands and deepens over time',
        icon: '🌍'
      },
      {
        title: 'Thematic Development',
        value: this.calculateThematicDevelopment(metrics),
        description: 'How themes are explored and developed',
        icon: '🎭'
      },
      {
        title: 'Narrative Momentum',
        value: this.calculateNarrativeMomentum(metrics),
        description: 'How well the story maintains reader engagement',
        icon: '⚡'
      }
    ];

    return insights.map(insight => `
      <div class="insight-card">
        <div class="insight-header">
          <span class="insight-icon">${insight.icon}</span>
          <span class="insight-title">${insight.title}</span>
        </div>
        <div class="insight-score ${this.getScoreClass(insight.value)}">
          ${insight.value}/100
        </div>
        <div class="insight-description">${insight.description}</div>
        <div class="insight-bar">
          <div class="insight-fill" style="width: ${insight.value}%"></div>
        </div>
      </div>
    `).join('');
  }

  private calculateCharacterArcStrength(metrics: any): number {
    // Based on protagonist agency, character evolution, and archetype balance
    const characterMetrics = this.state.successMetrics?.characterArchetypes;
    const powerMetrics = this.state.successMetrics?.powerProgression;

    if (!characterMetrics || !powerMetrics) return 50;

    return Math.round((characterMetrics.successScore + powerMetrics.successScore) / 2);
  }

  private calculateWorldComplexityGrowth(metrics: any): number {
    // Based on setting diversity, thematic depth, and immersion factors
    const settingScore = (metrics.settingCount / 8) * 40; // Max 40 for 8+ settings
    const themeScore = (metrics.themeCount / 8) * 40; // Max 40 for 8+ themes
    const complexityScore = Math.min(metrics.narrativeComplexity * 2, 20); // Max 20

    return Math.round(settingScore + themeScore + complexityScore);
  }

  private calculateThematicDevelopment(metrics: any): number {
    // Based on theme complexity, market appeal, and thematic depth
    const themeAnalysis = metrics.themeAnalysis;
    if (!themeAnalysis) return 50;

    const complexityScore = (themeAnalysis.averageComplexity / 10) * 50;
    const appealScore = (themeAnalysis.averageMarketAppeal / 10) * 30;
    const depthScore = (metrics.thematicDepth / 100) * 20;

    return Math.round(complexityScore + appealScore + depthScore);
  }

  private calculateNarrativeMomentum(metrics: any): number {
    // Based on emotional intensity, conflict escalation, and relationship volatility
    const emotionalMetrics = this.state.successMetrics?.emotionalIntensity;
    const conflictMetrics = this.state.successMetrics?.conflictEscalation;
    const relationshipMetrics = this.state.successMetrics?.relationshipVolatility;

    if (!emotionalMetrics || !conflictMetrics || !relationshipMetrics) return 50;

    return Math.round((emotionalMetrics.successScore + conflictMetrics.successScore + relationshipMetrics.successScore) / 3);
  }

  private setupEventListeners(): void {
    if (!this.container) return;

    // Use event delegation to handle clicks on dynamically created elements
    this.container.addEventListener('click', (e) => {
      const target = e.target as HTMLElement;

      // Tab navigation
      const tabElement = target.closest('.success-tab') as HTMLElement;
      if (tabElement) {
        e.preventDefault();
        const view = tabElement.dataset.view;
        if (view && view !== this.state.currentView) {
          this.state.currentView = view as any;
          this.render();
          this.setupEventListeners(); // Re-setup listeners after render
        }
        return;
      }

      // Indicator card clicks
      const cardElement = target.closest('.indicator-card') as HTMLElement;
      if (cardElement) {
        e.preventDefault();
        const view = cardElement.dataset.view;
        if (view && view !== this.state.currentView) {
          this.state.currentView = view as any;
          this.render();
          this.setupEventListeners(); // Re-setup listeners after render
        }
        return;
      }
    });
  }
}

export const storySuccessDashboard = new StorySuccessDashboard();
