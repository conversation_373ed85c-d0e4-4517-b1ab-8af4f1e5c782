import os
import glob
from pathlib import Path

def combine_json_files(input_path, output_file="combined_output.txt"):
    """
    Reads all JSON files from the specified path and combines their content into one text file.
    
    Args:
        input_path (str): Path to the directory containing JSON files
        output_file (str): Name of the output text file (default: "combined_output.txt")
    """
    
    # Convert to Path object for easier handling
    input_dir = Path(input_path)
    
    # Check if the input directory exists
    if not input_dir.exists():
        print(f"Error: Directory '{input_path}' does not exist.")
        return
    
    if not input_dir.is_dir():
        print(f"Error: '{input_path}' is not a directory.")
        return
    
    # Find all JSON files in the directory
    json_files = list(input_dir.glob("*.json"))
    
    if not json_files:
        print(f"No JSON files found in '{input_path}'")
        return
    
    print(f"Found {len(json_files)} JSON files to process...")
    
    combined_content = []
    processed_files = 0
    
    # Process each JSON file
    for json_file in json_files:
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Add a separator with filename for clarity
            # separator = f"\n{'='*50}\n"
            # separator += f"File: {json_file.name}\n"
            # separator += f"{'='*50}\n"
            separator = "\n\n"
            
            combined_content.append(separator)
            combined_content.append(content)
            combined_content.append("\n")  # Add newline after each file
            
            processed_files += 1
            print(f"Processed: {json_file.name}")
            
        except Exception as e:
            print(f"Error reading {json_file.name}: {str(e)}")
            continue
    
    # Write combined content to output file
    try:
        output_path = Path(output_file)
        with open(output_path, 'w', encoding='utf-8') as f:
            f.writelines(combined_content)
        
        print(f"\nSuccessfully combined {processed_files} files into '{output_file}'")
        print(f"Output file size: {output_path.stat().st_size} bytes")
        
    except Exception as e:
        print(f"Error writing output file: {str(e)}")

def main():
    """
    Main function to run the script with command line arguments or user input.
    """
    # import sys
    
    # Check if path was provided as command line argument
    # if len(sys.argv) > 1:
    #     input_path = sys.argv[1]
    #     output_file = sys.argv[2] if len(sys.argv) > 2 else "combined_output.txt"
    # else:
        # Get input from user
    input_path = "/Users/<USER>/work/chaptr/Marvel/vampire_data/outputs_chapters/"
    output_file = "/Users/<USER>/work/chaptr/Marvel/vampire_data/outputs_chapters/combined_output.txt"
    if not output_file:
        output_file = "combined_output.txt"
    
    # Run the combination process
    combine_json_files(input_path, output_file)

if __name__ == "__main__":
    main()