import PyPDF2
import re
import os
from typing import List, <PERSON><PERSON>

def extract_text_from_pdf(pdf_path: str) -> List[Tuple[int, str]]:
    """Extract text from each page of the PDF along with page numbers."""
    with open(pdf_path, 'rb') as file:
        reader = PyPDF2.PdfReader(file)
        pages = []
        for page_num in range(len(reader.pages)):
            page = reader.pages[page_num]
            text = page.extract_text() or ""
            pages.append((page_num, text))
    return pages

def detect_chapters(pages: List[Tuple[int, str]]) -> List[Tuple[int, str]]:
    """Detect chapter headings and consolidate by chapter number."""
    
    # Debug: Show sample text from first few pages
    print("=== DEBUG: Sample text from pages ===")
    for page_num, text in pages[:3]:  # Show first 3 pages
        print(f"\n--- Page {page_num + 1} (first 500 chars) ---")
        print(repr(text[:500]))  # Using repr to see special characters
        print(f"--- Page {page_num + 1} (readable) ---")
        print(text[:500])
    print("=== END DEBUG ===\n")
    
    # More flexible pattern that allows whitespace before "Chapter" and handles titles
    chapter_pattern = re.compile(r'^\s*(Chapter|CHAPTER)\s+(\d+)(?:\s*[:-]?\s*([^\n\r]+))?', re.MULTILINE | re.IGNORECASE)
    chapters = []
    seen_chapters = {}  # Track unique chapters by chapter number

    for page_num, text in pages:
        matches = chapter_pattern.findall(text)
        for match in matches:
            chapter_number = match[1]  # The digit group
            chapter_title = match[2].strip() if match[2] else ""  # The optional title
            
            # Create a clean chapter identifier
            if chapter_title:
                chapter_key = f"Chapter {chapter_number}: {chapter_title}"
            else:
                chapter_key = f"Chapter {chapter_number}"

            # Only add the first occurrence of a chapter number
            chapter_num_key = f"Chapter_{chapter_number}"
            if chapter_num_key not in seen_chapters:
                seen_chapters[chapter_num_key] = page_num
                chapters.append((page_num, chapter_key))
                print(f"Found: {chapter_key} on page {page_num + 1}")

    # Sort chapters by page number to maintain order
    chapters.sort(key=lambda x: x[0])
    return chapters

def split_pdf(pdf_path: str, output_dir: str) -> None:
    """Split the PDF into one file per unique chapter number."""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Extract text and detect chapters
    pages = extract_text_from_pdf(pdf_path)
    chapters = detect_chapters(pages)
    
    if not chapters:
        print("No chapters detected. Trying alternative detection methods...")
        
        # Try multiple alternative patterns
        patterns_to_try = [
            (r'(Chapter|CHAPTER)\s+(\d+)', "Basic Chapter + number"),
            (r'(?i)chapter\s*(\d+)', "Case insensitive chapter + number"),
            (r'Ch\.?\s*(\d+)', "Ch. + number"),
            (r'(\d+)\s*[-–]\s*', "Number + dash (in case chapters start with numbers)"),
            (r'^\s*(\d+)\.?\s+[A-Z]', "Number + period + capital letter (numbered sections)")
        ]
        
        for pattern_str, description in patterns_to_try:
            print(f"Trying pattern: {description}")
            pattern = re.compile(pattern_str, re.MULTILINE)
            temp_chapters = []
            
            for page_num, text in pages:
                matches = pattern.findall(text)
                if matches:
                    print(f"  Found matches on page {page_num + 1}: {matches}")
                for match in matches:
                    if isinstance(match, tuple):
                        chapter_number = match[1] if len(match) > 1 else match[0]
                    else:
                        chapter_number = match
                    chapter_key = f"Chapter {chapter_number}"
                    temp_chapters.append((page_num, chapter_key))
            
            if temp_chapters:
                print(f"Pattern '{description}' found {len(temp_chapters)} potential chapters")
                chapters = temp_chapters
                break
        
        if chapters:
            # Remove duplicates and sort
            seen = set()
            unique_chapters = []
            for page_num, chapter_key in chapters:
                chapter_num = chapter_key.split()[1]
                if chapter_num not in seen:
                    seen.add(chapter_num)
                    unique_chapters.append((page_num, chapter_key))
            chapters = sorted(unique_chapters, key=lambda x: x[0])
    
    if not chapters:
        print("Still no chapters detected. Please check your PDF format.")
        print("\n=== FINAL DEBUG: Searching for any occurrence of 'chapter' ===")
        for page_num, text in pages:
            lines = text.split('\n')
            for line_num, line in enumerate(lines):
                if 'chapter' in line.lower():
                    print(f"Page {page_num + 1}, Line {line_num + 1}: {repr(line.strip())}")
        print("=== END FINAL DEBUG ===")
        return
    
    print(f"Detected {len(chapters)} chapters:")
    for page_num, chapter_title in chapters:
        print(f"  - {chapter_title} (Page {page_num + 1})")
    
    # Add a sentinel to mark the end of the last chapter
    chapters.append((len(pages), "End"))
    
    # Read the original PDF for splitting
    with open(pdf_path, 'rb') as file:
        reader = PyPDF2.PdfReader(file)
        
        for i in range(len(chapters) - 1):
            start_page, chapter_title = chapters[i]
            end_page = chapters[i + 1][0]
            
            # Create a new PDF writer for this chapter
            writer = PyPDF2.PdfWriter()
            
            # Add pages for this chapter
            for page_num in range(start_page, end_page):
                writer.add_page(reader.pages[page_num])
            
            # Sanitize chapter title for filename
            safe_title = "".join(c for c in chapter_title if c.isalnum() or c in " _-").strip()
            # Replace multiple spaces with single space
            safe_title = re.sub(r'\s+', ' ', safe_title)
            output_filename = os.path.join(output_dir, f"{safe_title}.pdf")
            
            # Write the chapter PDF
            with open(output_filename, 'wb') as output_file:
                writer.write(output_file)
            
            print(f"Created: {output_filename}")

def main():
    pdf_path = "/Users/<USER>/work/chaptr/Marvel/vampire_data/original_data/ch_1_30.pdf"  # Replace with your PDF file path
    output_dir = "/Users/<USER>/work/chaptr/Marvel/vampire_data/split_data_30"  # Replace with your desired output directory
    split_pdf(pdf_path, output_dir)

if __name__ == "__main__":
    main()