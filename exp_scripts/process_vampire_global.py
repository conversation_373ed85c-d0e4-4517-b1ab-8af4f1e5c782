#!/usr/bin/env python3
"""
Process the vampire combined book data to create a global network for the visualization.
This script creates the global view using the consolidated data structure.
"""

import json
import gzip
import os
import networkx as nx

def load_vampire_global_data():
    """Load the consolidated vampire book data file."""
    vampire_global_file = "vampire_data/outputs/c1_10_combined_book.json"
    
    if not os.path.exists(vampire_global_file):
        print(f"ERROR: {vampire_global_file} not found")
        return None
    
    with open(vampire_global_file, 'r', encoding='utf-8') as f:
        return json.load(f)

def create_global_network_from_vampire_data(vampire_data):
    """Create a global network from the consolidated vampire data."""
    G = nx.Graph()
    
    # Add characters as nodes using consolidated_characters
    for char in vampire_data['consolidated_characters']:
        char_id = char['character_id']
        
        # Determine character community/family for coloring
        community = 0  # Default community
        if 'quinn' in char_id.lower():
            community = 1  # Protagonist
        elif 'vorden' in char_id.lower():
            community = 2  # Close allies
        elif 'peter' in char_id.lower():
            community = 2  # Close allies
        elif 'layla' in char_id.lower():
            community = 3  # Academy students
        elif 'erin' in char_id.lower():
            community = 3  # Academy students
        elif any(name in char_id.lower() for name in ['sergeant', 'instructor', 'teacher']):
            community = 4  # Authority figures
        elif 'bully' in char_id.lower() or 'unnamed' in char_id.lower():
            community = 5  # Antagonists/minor characters
        
        # Create comprehensive description
        description_parts = []
        if char.get('character_archetype'):
            description_parts.append(f"Archetype: {char['character_archetype']}")
        if char.get('overall_character_development'):
            description_parts.append(f"Development: {char['overall_character_development']}")
        if char.get('key_relationships'):
            relationships = ', '.join(char['key_relationships']) if isinstance(char['key_relationships'], list) else str(char['key_relationships'])
            description_parts.append(f"Key relationships: {relationships}")
        
        description = '. '.join(description_parts) if description_parts else f"Character appearing in {char['total_chapters_present']} chapters"
        
        G.add_node(char_id, **{
            'label': char['character_name'],
            'name': char['character_name'],
            'stories': char['total_chapters_present'],  # Number of chapters appeared in
            'overall_importance': char['overall_importance'],
            'character_archetype': char.get('character_archetype', 'unknown'),
            'first_appearance_chapter': char['first_appearance_chapter'],
            'last_appearance_chapter': char['last_appearance_chapter'],
            'chapters': list(range(char['first_appearance_chapter'], char['last_appearance_chapter'] + 1)),
            'aliases': char.get('all_aliases', []),
            'image': 'placeholder.jpg',
            'image_url': 'images/characters/default_character.svg',
            'description': description,
            'community': community,
            'url': f'#character/{char_id}',
            'x': 0,
            'y': 0,
            'size': char['overall_importance'] / 2.0,  # Scale importance to size
        })
    
    # Add relationships as edges using consolidated_relationships
    if 'consolidated_relationships' in vampire_data:
        for rel in vampire_data['consolidated_relationships']:
            char1_id = rel['character_a_id']
            char2_id = rel['character_b_id']

            # Only add edge if both characters exist as nodes
            if G.has_node(char1_id) and G.has_node(char2_id):
                # Create description from relationship data
                rel_description_parts = []
                if rel.get('overall_relationship_summary'):
                    rel_description_parts.append(rel['overall_relationship_summary'])
                if rel.get('relationship_evolution'):
                    rel_description_parts.append(f"Evolution: {rel['relationship_evolution']}")

                rel_description = '. '.join(rel_description_parts) if rel_description_parts else "Character relationship across multiple chapters"

                G.add_edge(char1_id, char2_id, **{
                    'weight': rel.get('overall_interaction_strength', 1),
                    'relationship_type': rel.get('relationship_classification', 'unknown'),
                    'chapters_together': rel.get('chapters_together', []),
                    'description': rel_description,
                })
    
    return G

def convert_to_sigma_format(G):
    """Convert NetworkX graph to Sigma.js format."""
    nodes = []
    edges = []
    
    for node_id, attrs in G.nodes(data=True):
        nodes.append({
            'key': node_id,
            'attributes': attrs
        })
    
    for source, target, attrs in G.edges(data=True):
        edges.append({
            'key': f"{source}_{target}",
            'source': source,
            'target': target,
            'attributes': attrs
        })
    
    return {
        'attributes': {},
        'options': {
            'allowSelfLoops': False,
            'multi': False,
            'type': 'undirected'
        },
        'nodes': nodes,
        'edges': edges
    }

def main():
    print("Processing Vampire global data...")
    
    # Load the consolidated vampire data
    vampire_data = load_vampire_global_data()
    if not vampire_data:
        print("Failed to load vampire data!")
        return
    
    print(f"Loaded data for {vampire_data['book_metadata']['chapters_analyzed']} chapters")
    print(f"Found {len(vampire_data['consolidated_characters'])} characters")
    print(f"Found {len(vampire_data['consolidated_relationships'])} relationships")
    
    # Create the global network
    G = create_global_network_from_vampire_data(vampire_data)
    
    print(f"Created global network with {G.number_of_nodes()} nodes and {G.number_of_edges()} edges")
    
    # Convert to Sigma format
    sigma_data = convert_to_sigma_format(G)
    
    # Ensure output directory exists
    output_dir = 'data'
    os.makedirs(output_dir, exist_ok=True)
    
    # Save as compressed JSON (this will be the global view file)
    output_file = os.path.join(output_dir, 'Vampire_characters_by_stories_full_processed.json.gz')
    with gzip.open(output_file, 'wt', encoding='utf-8') as f:
        json.dump(sigma_data, f, separators=(',', ':'))
    
    print(f"Saved global network to {output_file}")
    print("Global network processing complete!")

if __name__ == "__main__":
    main()
