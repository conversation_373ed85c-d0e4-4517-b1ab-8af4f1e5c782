import PyPDF2
import re
import os
from typing import List, <PERSON><PERSON>

def extract_text_from_pdf(pdf_path: str) -> List[Tuple[int, str]]:
    """Extract text from each page of the PDF along with page numbers."""
    with open(pdf_path, 'rb') as file:
        reader = PyPDF2.PdfReader(file)
        pages = []
        for page_num in range(len(reader.pages)):
            page = reader.pages[page_num]
            text = page.extract_text() or ""
            pages.append((page_num, text))
    return pages

def detect_chapters(pages: List[Tuple[int, str]]) -> List[Tuple[int, str]]:
    """Detect chapter headings and consolidate by chapter number."""
    chapter_pattern = re.compile(r'^(Chapter|CHAPTER)\s+(\d+)(?:\s*[:-]?\s*(.+))?$', re.MULTILINE)
    chapters = []
    seen_chapters = {}  # Track unique chapters by chapter number

    for page_num, text in pages:
        matches = chapter_pattern.findall(text)
        for match in matches:
            chapter_number = match[1]
            chapter_key = f"Chapter {chapter_number}"

            # Only add the first occurrence of a chapter number
            if chapter_key not in seen_chapters:
                seen_chapters[chapter_key] = page_num
                chapters.append((page_num, chapter_key))

    # Sort chapters by page number to maintain order
    chapters.sort(key=lambda x: x[0])
    return chapters

def split_pdf(pdf_path: str, output_dir: str) -> None:
    """Split the PDF into one file per unique chapter number."""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Extract text and detect chapters
    pages = extract_text_from_pdf(pdf_path)
    chapters = detect_chapters(pages)
    
    if not chapters:
        print("No chapters detected. Ensure the PDF has clear chapter headings (e.g., 'Chapter 1').")
        return
    
    # Add a sentinel to mark the end of the last chapter
    chapters.append((len(pages), "End"))
    
    # Read the original PDF for splitting
    with open(pdf_path, 'rb') as file:
        reader = PyPDF2.PdfReader(file)
        
        for i in range(len(chapters) - 1):
            start_page, chapter_title = chapters[i]
            end_page = chapters[i + 1][0]
            
            # Create a new PDF writer for this chapter
            writer = PyPDF2.PdfWriter()
            
            # Add pages for this chapter
            for page_num in range(start_page, end_page):
                writer.add_page(reader.pages[page_num])
            
            # Sanitize chapter title for filename
            safe_title = "".join(c for c in chapter_title if c.isalnum() or c in " _-").strip()
            output_filename = os.path.join(output_dir, f"{safe_title}.pdf")
            
            # Write the chapter PDF
            with open(output_filename, 'wb') as output_file:
                writer.write(output_file)
            
            print(f"Created: {output_filename}")

def main():
    pdf_path = "/Users/<USER>/work/chaptr/Marvel/vampire_data/original_data/ch_1_10.pdf"  # Replace with your PDF file path
    output_dir = "/Users/<USER>/work/chaptr/Marvel/vampire_data/split_data"  # Replace with your desired output directory
    split_pdf(pdf_path, output_dir)

if __name__ == "__main__":
    main()