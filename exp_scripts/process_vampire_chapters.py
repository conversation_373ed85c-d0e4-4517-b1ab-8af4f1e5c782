#!/usr/bin/env python3
"""
Process Vampire chapter JSON files and create compressed network data
for the Marvel-style graph visualization.
"""

import json
import gzip
import os
import networkx as nx

def load_chapter_data(chapter_file):
    """Load and parse a single chapter JSON file."""
    with open(chapter_file, 'r', encoding='utf-8') as f:
        return json.load(f)

def create_chapter_network(chapter_data):
    """Create a network graph from chapter data."""
    G = nx.Graph()
    
    # Add characters as nodes
    characters = {}
    for char in chapter_data['characters']:
        char_id = char['character_id']
        characters[char_id] = char
        
        # Determine character community/family for coloring
        community = 0  # Default community
        if 'quinn' in char_id.lower():
            community = 1  # Protagonist
        elif 'vorden' in char_id.lower():
            community = 2  # Close allies
        elif 'peter' in char_id.lower():
            community = 2  # Close allies
        elif 'layla' in char_id.lower():
            community = 3  # Academy students
        elif 'erin' in char_id.lower():
            community = 3  # Academy students
        elif any(name in char_id.lower() for name in ['sergeant', 'instructor', 'teacher']):
            community = 4  # Authority figures
        elif 'bully' in char_id.lower() or 'unnamed' in char_id.lower():
            community = 5  # Antagonists/minor characters
        
        # Create description from character data
        description_parts = []
        if char.get('character_development'):
            description_parts.append(f"Development: {char['character_development']}")
        if char.get('emotional_state'):
            description_parts.append(f"Emotional state: {char['emotional_state']}")
        if char.get('character_goals'):
            goals = ', '.join(char['character_goals']) if isinstance(char['character_goals'], list) else str(char['character_goals'])
            description_parts.append(f"Goals: {goals}")
        
        description = '. '.join(description_parts) if description_parts else f"Character from Chapter {chapter_data['chapter_metadata']['chapter_number']}"
        
        G.add_node(char_id, **{
            'label': char['character_name'],
            'name': char['character_name'],
            'stories': 1,  # Single chapter
            'chapter_importance': char['importance_score'],
            'presence_level': char['presence_level'],
            'emotional_state': char.get('emotional_state', 'unknown'),
            'actions': char.get('actions', []),
            'image': 'placeholder.jpg',
            'image_url': 'images/characters/default_character.svg',
            'description': description,
            'community': community,
            'url': f'#character/{char_id}',
            'x': 0,
            'y': 0,
            'size': char['importance_score'] / 2.0,  # Scale importance to size
        })
    
    # Add relationships as edges
    if 'relationships' in chapter_data:
        for rel in chapter_data['relationships']:
            char1_id = rel['character_a_id']
            char2_id = rel['character_b_id']

            # Only add edge if both characters exist as nodes
            if char1_id in characters and char2_id in characters:
                # Create description from relationship data
                rel_description_parts = []
                if rel.get('interaction_summary'):
                    rel_description_parts.append(rel['interaction_summary'])
                if rel.get('interaction_context'):
                    rel_description_parts.append(f"Context: {rel['interaction_context']}")

                rel_description = '. '.join(rel_description_parts) if rel_description_parts else "Character interaction"

                G.add_edge(char1_id, char2_id, **{
                    'weight': rel.get('interaction_strength', 1),
                    'relationship_type': rel.get('relationship_type', 'unknown'),
                    'interaction_context': rel.get('interaction_context', ''),
                    'description': rel_description,
                })
    
    return G

def convert_to_sigma_format(G, chapter_num):
    """Convert NetworkX graph to Sigma.js format."""
    # Convert to the format expected by the Marvel visualization
    nodes = []
    edges = []
    
    for node_id, attrs in G.nodes(data=True):
        nodes.append({
            'key': node_id,
            'attributes': attrs
        })
    
    for source, target, attrs in G.edges(data=True):
        edges.append({
            'key': f"{source}_{target}",
            'source': source,
            'target': target,
            'attributes': attrs
        })
    
    return {
        'attributes': {},
        'options': {
            'allowSelfLoops': False,
            'multi': False,
            'type': 'undirected'
        },
        'nodes': nodes,
        'edges': edges
    }

def main():
    vampire_data_dir = 'vampire_data/outputs'
    output_dir = 'data'
    
    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)
    
    # Process each chapter file (c1.json through c10.json based on vampire data)
    for i in range(1, 11):  # c1.json through c10.json
        chapter_file = os.path.join(vampire_data_dir, f'c{i}.json')
        
        if not os.path.exists(chapter_file):
            print(f"Warning: {chapter_file} not found, skipping...")
            continue
            
        print(f"Processing {chapter_file}...")
        
        # Load chapter data
        chapter_data = load_chapter_data(chapter_file)
        
        # Create network
        G = create_chapter_network(chapter_data)
        
        # Convert to Sigma format
        sigma_data = convert_to_sigma_format(G, i)
        
        # Save as compressed JSON
        output_file = os.path.join(output_dir, f'Vampire_characters_by_stories_c{i}.json.gz')
        with gzip.open(output_file, 'wt', encoding='utf-8') as f:
            json.dump(sigma_data, f, separators=(',', ':'))
        
        print(f"Saved chapter {i} network to {output_file}")
        print(f"  - {G.number_of_nodes()} characters")
        print(f"  - {G.number_of_edges()} relationships")
    
    print("Chapter processing complete!")

if __name__ == "__main__":
    main()
