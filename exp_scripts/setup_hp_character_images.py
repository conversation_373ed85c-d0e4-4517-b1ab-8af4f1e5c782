#!/usr/bin/env python3
"""
Smart mapping script for <PERSON> character images.
This script will:
1. Copy images from hp_images/ to images/characters/
2. Create intelligent mappings between character names and image files
3. Update the character image mapping in index.ts
"""

import os
import shutil
import json
import re
from difflib import SequenceMatcher

def similarity(a, b):
    """Calculate similarity between two strings."""
    return SequenceMatcher(None, a.lower(), b.lower()).ratio()

def get_character_names_from_data():
    """Extract character names from the all_chapters.json file."""
    characters = []
    
    # Load all_chapters.json to get character names
    all_chapters_file = "hp_data/all_chapters.json"
    if os.path.exists(all_chapters_file):
        with open(all_chapters_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            for char in data.get('consolidated_characters', []):
                characters.append({
                    'id': char['character_id'],
                    'name': char['character_name'],
                    'aliases': char.get('all_aliases', [])
                })
    
    return characters

def get_available_images():
    """Get list of available images in hp_images directory."""
    images = []
    hp_images_dir = "hp_images"
    
    if os.path.exists(hp_images_dir):
        for filename in os.listdir(hp_images_dir):
            if filename.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.svg')):
                name_part = os.path.splitext(filename)[0]
                images.append({
                    'filename': filename,
                    'name_part': name_part,
                    'full_path': os.path.join(hp_images_dir, filename)
                })
    
    return images

def create_smart_mappings(characters, images):
    """Create smart mappings between characters and images."""
    mappings = {}
    
    # Manual mappings for better accuracy
    manual_mappings = {
        'harry_potter': ['harry'],
        'hermione_granger': ['hermione'],
        'ron_weasley': ['ron'],
        'lord_voldemort': ['voldemort'],
        'remus_lupin': ['lupin'],
        'dolores_umbridge': ['umbridge'],
        'severus_snape': ['snape'],
        'draco_malfoy': ['draco'],
        'ginny_weasley': ['ginny'],
        'neville_longbottom': ['neville'],
        'luna_lovegood': ['luna'],
        'cho_chang': ['cho'],
        'bellatrix_lestrange': ['bellatrix'],
        'minerva_mcgonagall': ['mcgonagall']
    }
    
    # First, try manual mappings
    for char in characters:
        char_id = char['id']
        char_name = char['name']
        
        if char_id in manual_mappings:
            # Look for images that match the manual mapping
            for image in images:
                for manual_name in manual_mappings[char_id]:
                    if manual_name.lower() in image['name_part'].lower():
                        mappings[char_id] = {
                            'character_name': char_name,
                            'image_file': image['filename'],
                            'image_path': f"images/characters/hp_{image['filename']}",
                            'source_path': image['full_path'],
                            'match_type': 'manual'
                        }
                        break
                if char_id in mappings:
                    break
    
    # Then try fuzzy matching for unmapped characters
    for char in characters:
        char_id = char['id']
        char_name = char['name']
        
        if char_id not in mappings:
            best_match = None
            best_score = 0.0
            
            # Try matching against character name parts
            name_parts = char_name.lower().split()
            
            for image in images:
                # Check if any part of the character name matches the image name
                for name_part in name_parts:
                    score = similarity(name_part, image['name_part'])
                    if score > best_score and score > 0.6:  # Threshold for fuzzy matching
                        best_score = score
                        best_match = image
                
                # Also check aliases
                for alias in char.get('aliases', []):
                    alias_parts = alias.lower().split()
                    for alias_part in alias_parts:
                        score = similarity(alias_part, image['name_part'])
                        if score > best_score and score > 0.6:
                            best_score = score
                            best_match = image
            
            if best_match:
                mappings[char_id] = {
                    'character_name': char_name,
                    'image_file': best_match['filename'],
                    'image_path': f"images/characters/hp_{best_match['filename']}",
                    'source_path': best_match['full_path'],
                    'match_type': 'fuzzy',
                    'match_score': best_score
                }
    
    return mappings

def copy_images(mappings):
    """Copy images to the characters directory."""
    characters_dir = "images/characters"
    
    # Ensure the characters directory exists
    os.makedirs(characters_dir, exist_ok=True)
    
    copied_files = []
    
    for char_id, mapping in mappings.items():
        source_path = mapping['source_path']
        target_filename = f"hp_{mapping['image_file']}"
        target_path = os.path.join(characters_dir, target_filename)
        
        try:
            shutil.copy2(source_path, target_path)
            copied_files.append({
                'character': mapping['character_name'],
                'source': source_path,
                'target': target_path,
                'match_type': mapping['match_type']
            })
            print(f"✅ Copied {mapping['character_name']}: {source_path} -> {target_path}")
        except Exception as e:
            print(f"❌ Failed to copy {mapping['character_name']}: {e}")
    
    return copied_files

def generate_character_image_map(mappings):
    """Generate the JavaScript object for character image mapping."""
    js_mappings = {}
    
    for char_id, mapping in mappings.items():
        # Use the target path (with hp_ prefix)
        target_filename = f"hp_{mapping['image_file']}"
        js_mappings[char_id] = f"images/characters/{target_filename}"
    
    return js_mappings

def main():
    print("🎭 Harry Potter Character Image Mapping")
    print("=" * 50)
    
    # Get character data
    print("📚 Loading character data...")
    characters = get_character_names_from_data()
    print(f"Found {len(characters)} characters")
    
    # Get available images
    print("🖼️  Loading available images...")
    images = get_available_images()
    print(f"Found {len(images)} images")
    
    # Create smart mappings
    print("🧠 Creating smart mappings...")
    mappings = create_smart_mappings(characters, images)
    print(f"Created {len(mappings)} mappings")
    
    # Copy images
    print("📁 Copying images...")
    copied_files = copy_images(mappings)
    
    # Generate JavaScript mapping
    print("🔧 Generating JavaScript mapping...")
    js_mappings = generate_character_image_map(mappings)
    
    # Print results
    print("\n📋 Mapping Results:")
    print("-" * 30)
    for char_id, mapping in mappings.items():
        match_info = f"({mapping['match_type']}"
        if 'match_score' in mapping:
            match_info += f", score: {mapping['match_score']:.2f}"
        match_info += ")"
        print(f"  {mapping['character_name']} -> {mapping['image_file']} {match_info}")
    
    # Print JavaScript code to add to index.ts
    print("\n🔧 JavaScript mapping to add to index.ts:")
    print("-" * 50)
    print("const characterImageMap = {")
    for char_id, image_path in js_mappings.items():
        print(f"  '{char_id}': '{image_path}',")
    print("};")
    
    print(f"\n✅ Successfully processed {len(copied_files)} character images!")
    print("📝 Next step: Update the characterImageMap in index.ts with the mapping above.")

if __name__ == "__main__":
    main()
