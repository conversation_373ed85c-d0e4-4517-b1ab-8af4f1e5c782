#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import networkx as nx
from collections import defaultdict
import gzip
import shutil

def load_chapter_data():
    """Load all Harry Potter chapter JSON files from hp_data directory"""
    chapters = {}
    hp_data_dir = "hp_data"
    
    if not os.path.exists(hp_data_dir):
        print(f"ERROR: {hp_data_dir} directory not found")
        return {}
    
    for filename in os.listdir(hp_data_dir):
        if filename.endswith('.json'):
            chapter_num = filename.replace('c', '').replace('.json', '')
            try:
                chapter_num = int(chapter_num)
                with open(os.path.join(hp_data_dir, filename), 'r') as f:
                    chapters[chapter_num] = json.load(f)
                print(f"Loaded chapter {chapter_num}: {chapters[chapter_num]['chapter_metadata']['chapter_title']}")
            except (ValueError, KeyError) as e:
                print(f"Warning: Could not process {filename}: {e}")
    
    return chapters

def build_character_nodes(chapters):
    """Build character nodes from all chapters"""
    characters = {}
    
    for chapter_num, chapter_data in chapters.items():
        for char in chapter_data['characters']:
            char_id = char['character_id']
            
            if char_id not in characters:
                characters[char_id] = {
                    'id': char_id,
                    'label': char['character_name'],
                    'aliases': char.get('aliases', []),
                    'stories': 0,  # Total chapter appearances
                    'chapters': [],  # List of chapters appeared in
                    'total_importance': 0,
                    'description': '',
                    'image': '',  # We'll use a placeholder for now
                    'image_url': '',
                    'url': ''
                }
            
            # Aggregate data across chapters
            characters[char_id]['stories'] += 1
            characters[char_id]['chapters'].append(chapter_num)
            characters[char_id]['total_importance'] += char['importance_score']
            
            # Use the most detailed description found
            if char.get('character_development') and len(char['character_development']) > len(characters[char_id]['description']):
                characters[char_id]['description'] = char['character_development']
    
    return characters

def build_relationship_edges(chapters):
    """Build relationship edges from all chapters"""
    relationships = defaultdict(lambda: {
        'weight': 0,
        'chapters': [],
        'interactions': [],
        'relationship_types': set(),
        'total_strength': 0,
        'total_emotional_intensity': 0
    })
    
    for chapter_num, chapter_data in chapters.items():
        for rel in chapter_data['relationships']:
            char_a = rel['character_a_id']
            char_b = rel['character_b_id']
            
            # Create consistent edge key (alphabetical order)
            edge_key = tuple(sorted([char_a, char_b]))
            
            relationships[edge_key]['weight'] += 1
            relationships[edge_key]['chapters'].append(chapter_num)
            relationships[edge_key]['interactions'].append(rel['interaction_summary'])
            relationships[edge_key]['relationship_types'].add(rel['relationship_type'])
            relationships[edge_key]['total_strength'] += rel['strength_score']
            relationships[edge_key]['total_emotional_intensity'] += rel['emotional_intensity']
    
    return relationships

def create_chapter_specific_graph(chapter_data, all_characters):
    """Create a graph for a specific chapter"""
    G = nx.Graph()

    # Add nodes for characters in this chapter
    for char in chapter_data['characters']:
        char_id = char['character_id']
        char_info = all_characters[char_id]

        # Determine character community/family (simplified for now)
        community = 0  # Default community
        if 'potter' in char_id.lower():
            community = 1
        elif 'dursley' in char_id.lower():
            community = 2
        elif 'dumbledore' in char_id.lower():
            community = 3
        elif any(name in char_id.lower() for name in ['hermione', 'ron', 'weasley']):
            community = 4

        G.add_node(char_id, **{
            'label': char['character_name'],
            'name': char['character_name'],  # Add name attribute for compatibility
            'stories': char_info['stories'],  # Total across all chapters
            'chapter_importance': char['importance_score'],
            'presence_level': char['presence_level'],
            'emotional_state': char['emotional_state'],
            'actions': char['actions'],
            'image': char_info['image'] or 'placeholder.jpg',
            'image_url': char_info['image_url'] or 'images/placeholder.jpg',
            'description': char_info['description'],
            'community': community,  # For clustering/coloring
            'url': f'#character/{char_id}',  # Placeholder URL
            'x': 0,  # Will be set by layout algorithm
            'y': 0,  # Will be set by layout algorithm
            'size': 1,  # Will be computed based on importance
            'color': '#666666'  # Will be set based on community
        })

    # Add edges for relationships in this chapter
    for rel in chapter_data['relationships']:
        char_a = rel['character_a_id']
        char_b = rel['character_b_id']

        if G.has_node(char_a) and G.has_node(char_b):
            G.add_edge(char_a, char_b, **{
                'weight': rel['strength_score'],
                'relationship_type': rel['relationship_type'],
                'interaction_summary': rel['interaction_summary'],
                'emotional_intensity': rel['emotional_intensity'],
                'plot_significance': rel['plot_significance']
            })

    return G

def create_global_graph(characters, relationships):
    """Create the global graph with all characters and relationships"""
    G = nx.Graph()

    # Add all character nodes with attributes matching Marvel's format
    for char_id, char_data in characters.items():
        # Determine character community/family (simplified for now)
        community = 0  # Default community
        if 'potter' in char_id.lower():
            community = 1
        elif 'dursley' in char_id.lower():
            community = 2
        elif 'dumbledore' in char_id.lower():
            community = 3
        elif any(name in char_id.lower() for name in ['hermione', 'ron', 'weasley']):
            community = 4

        G.add_node(char_id, **{
            'label': char_data['label'],
            'name': char_data['label'],  # Add name attribute for compatibility
            'stories': char_data['stories'],
            'total_importance': char_data['total_importance'],
            'avg_importance': char_data['total_importance'] / char_data['stories'] if char_data['stories'] > 0 else 0,
            'chapters': char_data['chapters'],
            'image': char_data['image'] or 'placeholder.jpg',
            'image_url': char_data['image_url'] or 'images/placeholder.jpg',
            'description': char_data['description'],
            'community': community,  # For clustering/coloring
            'url': f'#character/{char_id}',  # Placeholder URL
            'x': 0,  # Will be set by layout algorithm
            'y': 0,  # Will be set by layout algorithm
            'size': 1,  # Will be computed based on stories
            'color': '#666666'  # Will be set based on community
        })

    # Add relationship edges
    for (char_a, char_b), rel_data in relationships.items():
        if G.has_node(char_a) and G.has_node(char_b):
            G.add_edge(char_a, char_b, **{
                'weight': rel_data['weight'],  # Number of chapters they interact
                'avg_strength': rel_data['total_strength'] / rel_data['weight'] if rel_data['weight'] > 0 else 0,
                'avg_emotional_intensity': rel_data['total_emotional_intensity'] / rel_data['weight'] if rel_data['weight'] > 0 else 0,
                'chapters': rel_data['chapters'],
                'relationship_types': list(rel_data['relationship_types']),
                'interactions': rel_data['interactions'][:3]  # Keep first 3 interactions as examples
            })

    return G

def save_graph_data(graph, filename):
    """Save graph in the format expected by the visualization (Graphology format)"""
    # Convert to Graphology JSON format that matches Marvel's structure
    graph_data = {
        'attributes': {},
        'options': {
            'allowSelfLoops': False,
            'multi': False,
            'type': 'undirected'
        },
        'nodes': [],
        'edges': []
    }

    # Add nodes in Graphology format
    for node_id, node_attrs in graph.nodes(data=True):
        graph_data['nodes'].append({
            'key': node_id,
            'attributes': node_attrs
        })

    # Add edges in Graphology format
    for source, target, edge_attrs in graph.edges(data=True):
        graph_data['edges'].append({
            'source': source,
            'target': target,
            'attributes': edge_attrs
        })

    # Save as JSON
    with open(filename, 'w') as f:
        json.dump(graph_data, f, indent=2)

    # Create compressed version using gzip (we'll handle pako compression later)
    with open(filename, 'rb') as f_in:
        with gzip.open(filename + '.gz', 'wb') as f_out:
            shutil.copyfileobj(f_in, f_out)

    print(f"Saved graph data to {filename} and {filename}.gz")

def process_harry_potter_data():
    """Main function to process Harry Potter data and create graph files"""
    print("Processing Harry Potter chapter data...")
    
    # Load all chapter data
    chapters = load_chapter_data()
    if not chapters:
        print("No chapter data found!")
        return
    
    print(f"Loaded {len(chapters)} chapters")
    
    # Build character and relationship data
    characters = build_character_nodes(chapters)
    relationships = build_relationship_edges(chapters)
    
    print(f"Found {len(characters)} characters and {len(relationships)} relationships")
    
    # Create data directory if it doesn't exist
    os.makedirs('data', exist_ok=True)
    
    # Create global graph
    global_graph = create_global_graph(characters, relationships)
    save_graph_data(global_graph, 'data/HarryPotter_characters_by_stories_full.json')
    
    # Create chapter-specific graphs
    chapter_graphs = {}
    for chapter_num, chapter_data in chapters.items():
        chapter_graph = create_chapter_specific_graph(chapter_data, characters)
        chapter_graphs[chapter_num] = chapter_graph
        save_graph_data(chapter_graph, f'data/HarryPotter_characters_chapter_{chapter_num}.json')
    
    # Save metadata about chapters
    chapter_metadata = {}
    for chapter_num, chapter_data in chapters.items():
        chapter_metadata[chapter_num] = chapter_data['chapter_metadata']
    
    with open('data/HarryPotter_chapters_metadata.json', 'w') as f:
        json.dump(chapter_metadata, f, indent=2)
    
    print("Harry Potter data processing complete!")
    print(f"Created global graph with {global_graph.number_of_nodes()} nodes and {global_graph.number_of_edges()} edges")
    print(f"Created {len(chapter_graphs)} chapter-specific graphs")

if __name__ == "__main__":
    process_harry_potter_data()
