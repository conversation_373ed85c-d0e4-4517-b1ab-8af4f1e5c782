#!/usr/bin/env python3
"""
Process the all_chapters.json file to create a global network for the Harry Potter visualization.
This script creates the global view using the consolidated data structure.
"""

import json
import gzip
import os
import sys
from collections import defaultdict
import networkx as nx

def load_all_chapters_data():
    """Load the consolidated all_chapters.json file."""
    all_chapters_file = "hp_data/all_chapters.json"
    
    if not os.path.exists(all_chapters_file):
        print(f"ERROR: {all_chapters_file} not found")
        return None
    
    with open(all_chapters_file, 'r', encoding='utf-8') as f:
        return json.load(f)

def create_global_network_from_all_chapters(all_chapters_data):
    """Create a global network graph from the all_chapters.json data."""
    G = nx.Graph()
    
    # Add characters as nodes using consolidated_characters
    for char in all_chapters_data['consolidated_characters']:
        char_id = char['character_id']
        
        # Determine character community/family for coloring
        community = 0  # Default community
        if 'potter' in char_id.lower():
            community = 1
        elif 'dursley' in char_id.lower():
            community = 2
        elif 'dumbledore' in char_id.lower():
            community = 3
        elif any(name in char_id.lower() for name in ['hermione', 'ron', 'weasley']):
            community = 4
        elif 'voldemort' in char_id.lower() or 'death_eater' in char_id.lower():
            community = 5
        elif 'order' in char_id.lower() or any(name in char_id.lower() for name in ['lupin', 'tonks', 'kingsley']):
            community = 6
        
        # Create description from character archetype and overall importance
        description = f"Character archetype: {char.get('character_archetype', 'unknown')}. "
        description += f"Appears in {char['total_chapters_present']} chapters. "
        description += f"Overall importance: {char['overall_importance']}/10."
        
        G.add_node(char_id, **{
            'label': char['character_name'],
            'name': char['character_name'],
            'stories': char['total_chapters_present'],  # Number of chapters appeared in
            'overall_importance': char['overall_importance'],
            'character_archetype': char.get('character_archetype', 'unknown'),
            'first_appearance_chapter': char['first_appearance_chapter'],
            'last_appearance_chapter': char['last_appearance_chapter'],
            'chapters': list(range(char['first_appearance_chapter'], char['last_appearance_chapter'] + 1)),
            'aliases': char.get('all_aliases', []),
            'image': 'placeholder.jpg',
            'image_url': 'images/characters/default_character.svg',
            'description': description,
            'community': community,
            'url': f'#character/{char_id}',
            'x': 0,
            'y': 0,
            'size': char['overall_importance'],  # Use overall importance for size
            'color': '#666666'
        })
    
    # Add relationships as edges using consolidated_relationships
    for rel in all_chapters_data['consolidated_relationships']:
        char_a = rel['character_a_id']
        char_b = rel['character_b_id']
        
        if G.has_node(char_a) and G.has_node(char_b):
            # Calculate average emotional intensity from interaction timeline
            total_emotional_intensity = 0
            interaction_count = len(rel.get('interaction_timeline', []))
            
            if interaction_count > 0:
                for interaction in rel['interaction_timeline']:
                    total_emotional_intensity += interaction.get('emotional_intensity', 0)
                avg_emotional_intensity = total_emotional_intensity / interaction_count
            else:
                avg_emotional_intensity = 5  # Default moderate intensity
            
            # Get relationship strength from evolution if available
            relationship_strength = 5  # Default
            if 'relationship_evolution' in rel and 'current_status' in rel['relationship_evolution']:
                # Try to infer strength from current status description
                status = rel['relationship_evolution']['current_status'].lower()
                if any(word in status for word in ['very strong', 'deep', 'close']):
                    relationship_strength = 8
                elif any(word in status for word in ['strong', 'stable']):
                    relationship_strength = 7
                elif any(word in status for word in ['good', 'positive']):
                    relationship_strength = 6
                elif any(word in status for word in ['weak', 'strained']):
                    relationship_strength = 3
                elif any(word in status for word in ['hostile', 'enemy']):
                    relationship_strength = 2
            
            G.add_edge(char_a, char_b, **{
                'relationship_type': rel['relationship_classification'],
                'relationship_summary': rel['relationship_summary'],
                'strength_score': relationship_strength,
                'emotional_intensity': avg_emotional_intensity,
                'interaction_count': interaction_count,
                'chapters_together': len(rel.get('interaction_timeline', [])),
                'weight': relationship_strength,
                'size': max(1, relationship_strength / 2),
                'color': '#ffffff'
            })
    
    return G

def convert_to_sigma_format(G):
    """Convert NetworkX graph to Sigma.js format compatible with the Marvel visualization."""
    nodes = []
    edges = []
    
    for node_id, attrs in G.nodes(data=True):
        nodes.append({
            'key': node_id,
            'attributes': attrs
        })
    
    for source, target, attrs in G.edges(data=True):
        edges.append({
            'key': f"{source}_{target}",
            'source': source,
            'target': target,
            'attributes': attrs
        })
    
    return {
        'attributes': {},
        'options': {
            'allowSelfLoops': False,
            'multi': False,
            'type': 'undirected'
        },
        'nodes': nodes,
        'edges': edges
    }

def main():
    """Main function to process all_chapters.json and create global network."""
    print("Processing all_chapters.json for global network...")
    
    # Load the consolidated data
    all_chapters_data = load_all_chapters_data()
    if not all_chapters_data:
        print("Failed to load all_chapters.json")
        return
    
    print(f"Loaded data for {all_chapters_data['book_metadata']['chapters_analyzed']} chapters")
    print(f"Found {len(all_chapters_data['consolidated_characters'])} characters")
    print(f"Found {len(all_chapters_data['consolidated_relationships'])} relationships")
    
    # Create the global network
    G = create_global_network_from_all_chapters(all_chapters_data)
    
    print(f"Created global network with {G.number_of_nodes()} nodes and {G.number_of_edges()} edges")
    
    # Convert to Sigma format
    sigma_data = convert_to_sigma_format(G)
    
    # Ensure output directory exists
    output_dir = 'data'
    os.makedirs(output_dir, exist_ok=True)
    
    # Save as compressed JSON (this will be the global view file)
    output_file = os.path.join(output_dir, 'HarryPotter_characters_by_stories_full_processed.json.gz')
    with gzip.open(output_file, 'wt', encoding='utf-8') as f:
        json.dump(sigma_data, f, separators=(',', ':'))
    
    print(f"Saved global network to {output_file}")
    print("Global network processing complete!")

if __name__ == "__main__":
    main()
