{Chapter Content}


--------------------------------------------------------------------
Task Overview
You are analyzing a chapter from a fiction manuscript to extract structured data for building an interactive character relationship knowledge graph. Extract comprehensive character and relationship information following the exact format specified below.

Input Context
Book Title: [BOOK_TITLE]
Chapter Number: [CHAPTER_NUMBER]
Chapter Title: [CHAPTER_TITLE]
Chapter Text: [CHAPTER_TEXT]
Required Output Format
Provide your response as a valid JSON object with the following structure:

{
"chapter_metadata": {
"chapter_number": number,
"chapter_title": "string",
"major_plot_events": ["event1", "event2", "event3"],
"chapter_themes": ["theme1", "theme2"],
"setting_locations": ["location1", "location2"],
"chapter_mood": "string",
"narrative_importance": number (1-10)
},
"characters": [
{
"character_id": "standardized_name_lowercase_underscore",
"character_name": "Full Character Name",
"aliases": ["nickname1", "alias1"],
"presence_level": "protagonist|major|supporting|minor|mentioned",
"importance_score": number (1-10),
"actions": [
"Specific action 1 the character performed",
"Specific action 2 the character performed",
"Specific action 3 the character performed",
"Specific action 4 the character performed",
"Specific action 5 the character performed"
],
"emotional_state": "primary_emotion",
"character_goals": ["goal1", "goal2"],
"character_conflicts": ["conflict1", "conflict2"],
"character_development": "How the character changed or grew in this chapter",
"dialogue_significance": number (1-10),
"is_new_character": boolean
}
],
"relationships": [
{
"character_a_id": "character_id_1",
"character_b_id": "character_id_2",
"relationship_type": "alliance|conflict|mentorship|romance|family|friendship|rivalry|protection|betrayal|neutral",
"interaction_summary": "2-3 sentence summary of how these characters interacted in this chapter",
"interaction_details": [
"Specific interaction detail 1",
"Specific interaction detail 2",
"Specific interaction detail 3"
],
"strength_score": number (1-10),
"emotional_intensity": number (1-10),
"dialogue_exchanges": number,
"relationship_change": number (-5 to +5),
"relationship_status": "improving|deteriorating|stable|new|ended",
"plot_significance": number (1-10),
"shared_scenes": ["scene_description_1", "scene_description_2"]
}
],
"extraction_metadata": {
"confidence_score": number (1-10),
"processing_notes": ["note1", "note2"],
"ambiguities": ["ambiguity1", "ambiguity2"],
"character_disambiguation": {
"character_id": "clarification_note"
}
}
}

Detailed Extraction Guidelines
Character Extraction Rules
Character Identification: Include all characters who speak, act, or are significantly mentioned
Character IDs: Use lowercase, underscore-separated format (e.g., "harry_potter", "hermione_granger")
Presence Levels:
Protagonist: Main focus of chapter, drives major action
Major: Significant role, multiple scenes or important actions
Supporting: Present in scenes, contributes to plot/dialogue
Minor: Brief appearance or mention with some relevance
Mentioned: Referenced but not present
Actions: Focus on concrete, specific actions, not general descriptions
Importance Score: Based on narrative impact, screen time, and plot relevance
Relationship Extraction Rules
Relationship Types:
Alliance: Working together, cooperation, shared goals
Conflict: Opposition, argument, fighting, tension
Mentorship: Teaching, guidance, learning dynamic
Romance: Romantic feelings, attraction, romantic interaction
Family: Blood relations, parental/sibling dynamics
Friendship: Personal bond, loyalty, emotional support
Rivalry: Competition, jealousy, one-upmanship
Protection: One character protecting/defending another
Betrayal: Breaking trust, deception, switching sides
Neutral: Interaction without strong emotional valence
Strength Scoring (1-10):
1-2: Minimal interaction, brief mention
3-4: Light interaction, casual conversation
5-6: Moderate interaction, meaningful exchange
7-8: Strong interaction, significant emotional content
9-10: Intense interaction, pivotal to plot or character
Relationship Change (-5 to +5):
Negative: Relationship deteriorated
Zero: No significant change
Positive: Relationship improved/strengthened
Quality Requirements
Be Specific: Avoid vague descriptions, provide concrete details
Stay Objective: Focus on observable actions and dialogue, not speculation
Maintain Consistency: Use consistent character IDs and naming
Capture Nuance: Include subtle relationship dynamics and character development
Note Ambiguities: Flag unclear character identities or relationship interpretations
Example Scenarios
If characters have a heated argument, mark as "conflict" with high emotional_intensity
If characters work together to solve a problem, mark as "alliance"
If a character comforts another, consider "friendship" or "family" based on context
If characters meet for the first time, note relationship_status as "new"
Critical Instructions
Extract ONLY information explicitly present in the chapter text
Do not infer information from general book knowledge
Maintain character ID consistency across all relationships
Include ALL significant character pairs, even brief interactions
Provide confidence scores honestly - flag uncertain extractions
Focus on THIS chapter's content, not broader story arcs
Generate the complete JSON response following this exact format for the provided chapter.