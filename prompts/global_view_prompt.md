# Global Character Relationship Consolidation Prompt

## Task Overview
You are consolidating multiple chapter-level character and relationship extractions into a comprehensive global knowledge graph for a fiction manuscript. Your task is to merge character data, track development across chapters, and create unified relationship profiles that span the entire analyzed portion of the book.

## Input Context
```
Book Title: [BOOK_TITLE]
Total Chapters Analyzed: [CHAPTER_COUNT]
Chapter Data: [MULTIPLE_CHAPTER_JSON_OUTPUTS]
```

## Required Output Format
Provide your response as a valid JSON object with the following structure:

```json
{
  "book_metadata": {
    "book_title": "string",
    "chapters_analyzed": number,
    "chapter_range": "X-Y",
    "overall_themes": ["theme1", "theme2", "theme3"],
    "major_plot_arcs": ["arc1", "arc2", "arc3"],
    "primary_settings": ["location1", "location2"],
    "narrative_progression": "string describing overall story movement"
  },
  "consolidated_characters": [
    {
      "character_id": "standardized_name_lowercase_underscore",
      "character_name": "Full Character Name",
      "all_aliases": ["nickname1", "alias1", "title1"],
      "overall_importance": number (1-10),
      "character_archetype": "protagonist|antagonist|mentor|love_interest|comic_relief|supporting|minor",
      "first_appearance_chapter": number,
      "last_appearance_chapter": number,
      "total_chapters_present": number,
      "chapter_by_chapter_summary": [
        {
          "chapter_number": number,
          "presence_level": "protagonist|major|supporting|minor|mentioned",
          "key_actions": ["action1", "action2", "action3"],
          "emotional_state": "primary_emotion",
          "character_goals": ["goal1", "goal2"],
          "development_notes": "How character changed/grew in this chapter"
        }
      ],
      "character_evolution": {
        "initial_state": "Character's starting point",
        "major_turning_points": [
          {
            "chapter": number,
            "event": "What happened",
            "impact": "How it changed the character"
          }
        ],
        "final_state": "Character's ending point in analyzed chapters",
        "growth_trajectory": "overall|positive|negative|cyclical|static"
      },
      "core_motivations": ["motivation1", "motivation2"],
      "primary_conflicts": ["internal_conflict1", "external_conflict1"],
      "speaking_patterns": "Notable dialogue characteristics",
      "relationship_centrality": number (1-10)
    }
  ],
  "consolidated_relationships": [
    {
      "relationship_id": "character_a_id__character_b_id",
      "character_a_id": "character_id_1",
      "character_b_id": "character_id_2",
      "relationship_classification": "alliance|conflict|mentorship|romance|family|friendship|rivalry|protection|betrayal|complex",
      "relationship_summary": "2-3 sentence overview of this relationship across all chapters",
      "relationship_evolution": {
        "initial_dynamic": "How relationship started",
        "key_developments": [
          {
            "chapter": number,
            "event": "What happened between them",
            "relationship_change": number (-5 to +5),
            "new_dynamic": "How relationship shifted"
          }
        ],
        "current_status": "Where relationship stands at end of analyzed chapters"
      },
      "interaction_timeline": [
        {
          "chapter": number,
          "interaction_type": "dialogue|action|conflict|cooperation|revelation",
          "interaction_summary": "Brief description of interaction",
          "emotional_intensity": number (1-10),
          "plot_significance": number (1-10)
        }
      ],
      "overall_strength": number (1-10),
      "relationship_stability": "stable|volatile|improving|deteriorating|complex",
      "mutual_influence": "How these characters affect each other",
      "shared_history": ["significant_event1", "significant_event2"],
      "future_implications": "Likely trajectory based on current dynamic"
    }
  ],
  "character_network_analysis": {
    "most_connected_characters": [
      {
        "character_id": "character_id",
        "connection_count": number,
        "network_influence": number (1-10)
      }
    ],
    "character_clusters": [
      {
        "cluster_name": "group_name",
        "members": ["character_id1", "character_id2"],
        "cluster_type": "family|allies|enemies|organization|location_based",
        "binding_factor": "What connects this group"
      }
    ],
    "relationship_patterns": [
      "pattern1: description",
      "pattern2: description"
    ]
  },
  "narrative_insights": {
    "character_development_trends": [
      "Characters who grew the most",
      "Characters who declined",
      "Static characters"
    ],
    "relationship_dynamics": [
      "Strongest alliances",
      "Major conflicts",
      "Romantic developments",
      "Betrayals and reconciliations"
    ],
    "plot_driving_relationships": [
      {
        "relationship_id": "character_a__character_b",
        "plot_impact": "How this relationship drives the story"
      }
    ],
    "character_agency_ranking": [
      {
        "character_id": "character_id",
        "agency_score": number (1-10),
        "influence_type": "drives_plot|reactive|supportive|observational"
      }
    ]
  },
  "consolidation_metadata": {
    "processing_confidence": number (1-10),
    "data_quality_notes": ["note1", "note2"],
    "character_disambiguation_log": [
      {
        "character_id": "character_id",
        "disambiguation_notes": "How conflicting data was resolved",
        "confidence": number (1-10)
      }
    ],
    "relationship_merge_notes": [
      "How overlapping relationships were consolidated"
    ],
    "gaps_and_limitations": [
      "What information might be missing or uncertain"
    ]
  }
}
```

## Detailed Consolidation Guidelines

### Character Consolidation Rules

1. **Character Identity Matching**
   - Match characters across chapters using character_id, character_name, and aliases
   - Flag potential duplicates with different IDs but similar names/descriptions
   - Resolve naming inconsistencies (e.g., "John" vs "Johnny" vs "John Smith")

2. **Importance Scoring**
   - Calculate overall_importance based on frequency, presence_level, and plot impact across chapters
   - Weight recent chapters more heavily if character importance is growing
   - Consider dialogue volume and action frequency

3. **Character Evolution Tracking**
   - Identify clear character arcs and development patterns
   - Note major turning points where character significantly changes
   - Track goal progression and conflict resolution

### Relationship Consolidation Rules

1. **Relationship Merging**
   - Combine all interactions between the same character pair across chapters
   - Track relationship progression chronologically
   - Identify patterns in relationship dynamics (cyclical conflicts, growing trust, etc.)

2. **Relationship Classification**
   - Use "complex" for relationships that don't fit single categories or change types
   - Prioritize the most recent or dominant relationship type
   - Note if relationship type evolves (enemy → ally, friend → romantic interest)

3. **Strength and Stability Assessment**
   - Overall strength = average of chapter-level strength scores, weighted by interaction frequency
   - Stability based on consistency of relationship_change scores across chapters

### Quality and Consistency Requirements

1. **Character ID Consistency**
   - Maintain consistent character_id format across all consolidated data
   - Create disambiguation log for any character identity conflicts
   - Use primary character name as it appears most frequently

2. **Timeline Accuracy**
   - Preserve chronological order in all timeline arrays
   - Ensure chapter numbers are accurate and sequential
   - Flag any timeline inconsistencies in processing notes

3. **Data Integrity**
   - Cross-reference relationships with character presence (can't have strong relationship if characters don't appear together)
   - Validate that relationship participants exist in character list
   - Ensure numerical scores are within specified ranges

### Analysis Depth Requirements

1. **Character Agency Assessment**
   - Identify characters who drive plot vs. those who react
   - Score based on initiative, decision-making, and impact on other characters
   - Note characters whose agency changes over time

2. **Network Analysis**
   - Identify central characters who connect different character groups
   - Map character clusters based on frequent interactions
   - Analyze relationship patterns for narrative significance

3. **Narrative Insights**
   - Identify the most plot-significant relationships
   - Track character development trajectories
   - Note recurring relationship patterns or themes

## Critical Instructions

- **Preserve Chapter-Level Detail**: Don't lose important chapter-specific information in the consolidation
- **Handle Contradictions**: When chapter data conflicts, prioritize most recent/reliable information and note discrepancies
- **Maintain Objectivity**: Base consolidation on provided data, don't add external book knowledge
- **Flag Uncertainties**: Use confidence scores and notes to indicate where consolidation involved interpretation
- **Comprehensive Coverage**: Include ALL characters and relationships from input data, even minor ones
- **Cross-Reference Validation**: Ensure all relationship participants exist in character list with consistent IDs

Generate the complete consolidated JSON response following this exact format for the provided multi-chapter data.


{Multiple chapter json output from Step 1 goes here}