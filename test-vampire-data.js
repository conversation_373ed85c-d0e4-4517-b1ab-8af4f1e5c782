const fs = require('fs');
const pako = require('pako');

console.log("🧛 Testing Vampire Data Files");
console.log("==============================");

// Test global vampire data
console.log("\n📊 Testing Global Vampire Data:");
try {
  const globalData = fs.readFileSync('data/Vampire_characters_by_stories_full_processed.json.gz');
  const globalGraph = JSON.parse(pako.inflate(globalData, {to: 'string'}));
  console.log(`✅ Global data loaded successfully`);
  console.log(`   - Nodes: ${globalGraph.nodes.length}`);
  console.log(`   - Edges: ${globalGraph.edges.length}`);
  console.log(`   - Sample characters: ${globalGraph.nodes.slice(0, 3).map(n => n.attributes.name).join(', ')}`);
} catch (e) {
  console.log(`❌ Global data ERROR: ${e.message}`);
}

// Test chapter vampire data
console.log("\n📖 Testing Chapter Vampire Data:");
const chapters = {};
for (let i = 1; i <= 10; i++) {
  try {
    const data = fs.readFileSync(`data/Vampire_characters_by_stories_c${i}.json.gz`);
    const graph = JSON.parse(pako.inflate(data, {to: 'string'}));
    chapters[i] = {
      nodes: graph.nodes.length,
      edges: graph.edges.length,
      characters: graph.nodes.map(n => n.attributes.name).sort()
    };
    console.log(`✅ Chapter ${i}: ${chapters[i].nodes} characters, ${chapters[i].edges} relationships`);
    console.log(`   Characters: ${chapters[i].characters.slice(0, 3).join(', ')}${chapters[i].characters.length > 3 ? '...' : ''}`);
  } catch (e) {
    console.log(`❌ Chapter ${i}: ERROR - ${e.message}`);
  }
}

// Test data structure consistency
console.log("\n🔍 Data Structure Analysis:");
try {
  const globalData = fs.readFileSync('data/Vampire_characters_by_stories_full_processed.json.gz');
  const globalGraph = JSON.parse(pako.inflate(globalData, {to: 'string'}));
  
  console.log("Global data structure:");
  console.log(`   - Has 'nodes' array: ${Array.isArray(globalGraph.nodes)}`);
  console.log(`   - Has 'edges' array: ${Array.isArray(globalGraph.edges)}`);
  console.log(`   - Has 'options' object: ${typeof globalGraph.options === 'object'}`);
  
  if (globalGraph.nodes.length > 0) {
    const sampleNode = globalGraph.nodes[0];
    console.log("Sample node structure:");
    console.log(`   - Has 'key': ${!!sampleNode.key}`);
    console.log(`   - Has 'attributes': ${!!sampleNode.attributes}`);
    console.log(`   - Sample attributes: ${Object.keys(sampleNode.attributes).slice(0, 5).join(', ')}`);
  }
  
  if (globalGraph.edges.length > 0) {
    const sampleEdge = globalGraph.edges[0];
    console.log("Sample edge structure:");
    console.log(`   - Has 'key': ${!!sampleEdge.key}`);
    console.log(`   - Has 'source': ${!!sampleEdge.source}`);
    console.log(`   - Has 'target': ${!!sampleEdge.target}`);
    console.log(`   - Has 'attributes': ${!!sampleEdge.attributes}`);
  }
} catch (e) {
  console.log(`❌ Structure analysis ERROR: ${e.message}`);
}

console.log("\n🎉 Vampire data testing complete!");
