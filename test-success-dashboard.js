/**
 * Test Story Success Dashboard functionality
 */

const fs = require('fs');

console.log('🎯 Testing Story Success Dashboard');
console.log('==================================\n');

// Test vampire data loading
console.log('📊 Testing Vampire Data for Success Analysis:');
try {
  const vampireData = JSON.parse(fs.readFileSync('vampire_data/outputs_combined/c_30_combined_book.json', 'utf8'));
  console.log(`✅ Vampire data loaded successfully`);
  console.log(`   - Book: ${vampireData.book_metadata.book_title}`);
  console.log(`   - Chapters: ${vampireData.book_metadata.chapters_analyzed}`);
  console.log(`   - Characters: ${vampireData.consolidated_characters.length}`);
  console.log(`   - Relationships: ${vampireData.consolidated_relationships.length}`);
  
  // Test success metrics data availability
  console.log('\n🔍 Success Metrics Data Availability:');
  
  // Check protagonist data
  const protagonist = vampireData.consolidated_characters.find(char => 
    char.character_archetype?.includes('protagonist') || char.overall_importance === 10
  );
  console.log(`   - Protagonist found: ${protagonist ? '✅' : '❌'} (${protagonist?.character_name || 'None'})`);
  
  if (protagonist) {
    console.log(`   - Character evolution data: ${protagonist.character_evolution ? '✅' : '❌'}`);
    console.log(`   - Chapter summaries: ${protagonist.chapter_by_chapter_summary?.length || 0} chapters`);
    console.log(`   - Turning points: ${protagonist.character_evolution?.major_turning_points?.length || 0}`);
  }
  
  // Check relationship data
  const relationships = vampireData.consolidated_relationships || [];
  console.log(`   - Relationships with interactions: ${relationships.filter(r => r.interaction_timeline?.length > 0).length}`);
  console.log(`   - Volatile relationships: ${relationships.filter(r => r.relationship_stability === 'volatile').length}`);
  
  // Check emotional intensity data
  const allInteractions = relationships.flatMap(rel => rel.interaction_timeline || []);
  const intensityPeaks = allInteractions.filter(interaction => interaction.emotional_intensity >= 8);
  console.log(`   - High intensity interactions (8+): ${intensityPeaks.length}`);
  console.log(`   - Max emotional intensity: ${Math.max(...allInteractions.map(i => i.emotional_intensity || 0))}`);
  
  // Check metadata
  const metadata = vampireData.book_metadata || {};
  console.log(`   - Themes available: ${metadata.overall_themes?.length || 0}`);
  console.log(`   - Settings available: ${metadata.primary_settings?.length || 0}`);
  console.log(`   - Plot arcs available: ${metadata.major_plot_arcs?.length || 0}`);
  
  // Check narrative insights
  const insights = vampireData.narrative_insights || {};
  console.log(`   - Character agency ranking: ${insights.character_agency_ranking?.length || 0} characters`);
  console.log(`   - Character development trends: ${insights.character_development_trends ? '✅' : '❌'}`);
  
  console.log('\n🎯 Success Pattern Analysis Preview:');
  
  // Power progression analysis
  if (protagonist?.character_evolution) {
    const turningPoints = protagonist.character_evolution.major_turning_points || [];
    const chapters = protagonist.chapter_by_chapter_summary || [];
    console.log(`   - Power progression events: ${turningPoints.length} major turning points`);
    console.log(`   - Average chapters between growth: ${chapters.length / Math.max(turningPoints.length, 1)}`);
    console.log(`   - Growth trajectory: ${protagonist.character_evolution.growth_trajectory || 'unknown'}`);
  }
  
  // Emotional intensity analysis
  if (intensityPeaks.length > 0) {
    const chapterSpread = allInteractions.map(i => i.chapter).filter(Boolean);
    const totalChapters = Math.max(...chapterSpread) - Math.min(...chapterSpread) + 1;
    console.log(`   - Emotional peak frequency: ${(intensityPeaks.length / totalChapters * 100).toFixed(1)}% of chapters`);
  }
  
  // Secret complexity analysis
  const secretRelationships = relationships.filter(rel => 
    rel.relationship_summary?.toLowerCase().includes('secret') ||
    rel.relationship_summary?.toLowerCase().includes('hidden')
  );
  console.log(`   - Secret-based relationships: ${secretRelationships.length}`);
  
  // Character archetype analysis
  const archetypes = vampireData.consolidated_characters.reduce((acc, char) => {
    const archetype = char.character_archetype || 'unknown';
    acc[archetype] = (acc[archetype] || 0) + 1;
    return acc;
  }, {});
  console.log(`   - Character archetype distribution:`, archetypes);
  
  console.log('\n🚀 Success Dashboard Ready!');
  console.log('   All required data structures are available for comprehensive success analysis.');
  
} catch (e) {
  console.log(`❌ Vampire data ERROR: ${e.message}`);
}

console.log('\n🎉 Success Dashboard testing complete!');
