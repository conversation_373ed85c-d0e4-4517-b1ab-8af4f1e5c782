<!DOCTYPE html>
<html>
<head>
    <title>Story Explorer Dataset Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { margin: 5px; padding: 10px; }
    </style>
</head>
<body>
    <h1>🧪 Story Explorer Dataset Test</h1>
    
    <div class="test-section">
        <h3>Test Instructions:</h3>
        <ol>
            <li>Open the main application at <a href="http://localhost:3000" target="_blank">http://localhost:3000</a></li>
            <li>Switch to "Vampire" dataset using the dropdown in the sidebar</li>
            <li>Open the Story Intelligence Dashboard</li>
            <li>Verify it shows Vampire data (8 characters, 4 relationships)</li>
            <li>Click on "Story Explorer" tab in the dashboard</li>
            <li>Check if it maintains Vampire dataset or switches back to <PERSON></li>
        </ol>
    </div>

    <div class="test-section">
        <h3>Expected Behavior:</h3>
        <ul>
            <li class="success">✅ Story Explorer should maintain the current dataset (Vampire)</li>
            <li class="success">✅ Character list should show Vampire characters (Quinn, Vorden, Peter, etc.)</li>
            <li class="success">✅ No automatic switch back to Harry Potter</li>
            <li class="success">✅ Dashboard header should still show "Vampire" in the dataset selector</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>Previous Issue (Now Fixed):</h3>
        <ul>
            <li class="error">❌ Story Explorer was calling storyIntelligence.initialize() without dataset parameter</li>
            <li class="error">❌ This defaulted to 'HarryPotter' and overrode the current dataset</li>
            <li class="error">❌ Dashboard would switch back to Harry Potter data unexpectedly</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>Fix Applied:</h3>
        <ul>
            <li class="success">✅ Modified storyIntelligence.initialize() to use current dataset when no parameter provided</li>
            <li class="success">✅ Added setCurrentDataset() method to sync with main application</li>
            <li class="success">✅ Main app now updates story intelligence service when switching datasets</li>
            <li class="success">✅ Story Explorer now respects the current dataset selection</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>Test Results:</h3>
        <div id="test-results">
            <p class="info">🔄 Please perform the manual test above and report results...</p>
        </div>
    </div>

    <script>
        // Simple test logging
        function logTest(message, type = 'info') {
            const results = document.getElementById('test-results');
            const p = document.createElement('p');
            p.className = type;
            p.textContent = message;
            results.appendChild(p);
        }

        // Auto-check if we can access the main app
        fetch('http://localhost:3000')
            .then(response => {
                if (response.ok) {
                    logTest('✅ Main application is running at localhost:3000', 'success');
                } else {
                    logTest('❌ Main application not accessible', 'error');
                }
            })
            .catch(error => {
                logTest('❌ Cannot connect to main application - make sure npm start is running', 'error');
            });
    </script>
</body>
</html>
