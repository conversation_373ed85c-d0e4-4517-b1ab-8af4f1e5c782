const fs = require('fs');
const pako = require('pako');

console.log("🧛 Testing 30-Chapter Vampire Data Files");
console.log("========================================");

// Test global 30-chapter vampire data
console.log("\n📊 Testing Global 30-Chapter Vampire Data:");
try {
  const globalData = fs.readFileSync('data/Vampire30_characters_by_stories_full_processed.json.gz');
  const globalGraph = JSON.parse(pako.inflate(globalData, {to: 'string'}));
  console.log(`✅ Global data loaded successfully`);
  console.log(`   - Nodes: ${globalGraph.nodes.length}`);
  console.log(`   - Edges: ${globalGraph.edges.length}`);
  console.log(`   - Sample characters: ${globalGraph.nodes.slice(0, 3).map(n => n.attributes.name).join(', ')}`);
} catch (e) {
  console.log(`❌ Global data ERROR: ${e.message}`);
}

// Test chapter vampire data
console.log("\n📖 Testing 30-Chapter Vampire Data:");
const chapters = {};
let totalChapters = 0;
let totalCharacters = 0;
let totalRelationships = 0;

for (let i = 1; i <= 30; i++) {
  try {
    const data = fs.readFileSync(`data/Vampire30_characters_by_stories_c${i}.json.gz`);
    const graph = JSON.parse(pako.inflate(data, {to: 'string'}));
    chapters[i] = {
      nodes: graph.nodes.length,
      edges: graph.edges.length,
      characters: graph.nodes.map(n => n.attributes.name).sort()
    };
    totalChapters++;
    totalCharacters += chapters[i].nodes;
    totalRelationships += chapters[i].edges;
    
    if (i <= 5 || i >= 26) { // Show first 5 and last 5 chapters
      console.log(`✅ Chapter ${i}: ${chapters[i].nodes} characters, ${chapters[i].edges} relationships`);
      console.log(`   Characters: ${chapters[i].characters.slice(0, 3).join(', ')}${chapters[i].characters.length > 3 ? '...' : ''}`);
    } else if (i === 6) {
      console.log(`   ... (chapters 6-25 processed successfully) ...`);
    }
  } catch (e) {
    console.log(`❌ Chapter ${i}: ERROR - ${e.message}`);
  }
}

// Test data structure consistency
console.log("\n🔍 30-Chapter Data Analysis:");
try {
  const globalData = fs.readFileSync('data/Vampire30_characters_by_stories_full_processed.json.gz');
  const globalGraph = JSON.parse(pako.inflate(globalData, {to: 'string'}));
  
  console.log("Global data structure:");
  console.log(`   - Has 'nodes' array: ${Array.isArray(globalGraph.nodes)}`);
  console.log(`   - Has 'edges' array: ${Array.isArray(globalGraph.edges)}`);
  console.log(`   - Has 'options' object: ${typeof globalGraph.options === 'object'}`);
  
  if (globalGraph.nodes.length > 0) {
    const sampleNode = globalGraph.nodes[0];
    console.log("Sample node structure:");
    console.log(`   - Has 'key': ${!!sampleNode.key}`);
    console.log(`   - Has 'attributes': ${!!sampleNode.attributes}`);
    console.log(`   - Sample attributes: ${Object.keys(sampleNode.attributes).slice(0, 5).join(', ')}`);
  }
  
  if (globalGraph.edges.length > 0) {
    const sampleEdge = globalGraph.edges[0];
    console.log("Sample edge structure:");
    console.log(`   - Has 'key': ${!!sampleEdge.key}`);
    console.log(`   - Has 'source': ${!!sampleEdge.source}`);
    console.log(`   - Has 'target': ${!!sampleEdge.target}`);
    console.log(`   - Has 'attributes': ${!!sampleEdge.attributes}`);
  }
} catch (e) {
  console.log(`❌ Structure analysis ERROR: ${e.message}`);
}

// Summary statistics
console.log("\n📈 Summary Statistics:");
console.log(`   - Total chapters processed: ${totalChapters}/30`);
console.log(`   - Average characters per chapter: ${(totalCharacters / totalChapters).toFixed(1)}`);
console.log(`   - Average relationships per chapter: ${(totalRelationships / totalChapters).toFixed(1)}`);
console.log(`   - Total character instances across all chapters: ${totalCharacters}`);
console.log(`   - Total relationship instances across all chapters: ${totalRelationships}`);

// Test Story Intelligence Dashboard data
console.log("\n📚 Testing Story Intelligence Dashboard Data:");
try {
  const dashboardData = JSON.parse(fs.readFileSync('vampire_data/outputs_combined/c_30_combined_book.json', 'utf8'));
  console.log(`✅ Dashboard data loaded successfully`);
  console.log(`   - Book: ${dashboardData.book_metadata.book_title}`);
  console.log(`   - Chapters analyzed: ${dashboardData.book_metadata.chapters_analyzed}`);
  console.log(`   - Chapter range: ${dashboardData.book_metadata.chapter_range}`);
  console.log(`   - Characters: ${dashboardData.consolidated_characters.length}`);
  console.log(`   - Relationships: ${dashboardData.consolidated_relationships.length}`);
  console.log(`   - Themes: ${dashboardData.book_metadata.overall_themes.length}`);
  console.log(`   - Plot arcs: ${dashboardData.book_metadata.major_plot_arcs.length}`);
} catch (e) {
  console.log(`❌ Dashboard data ERROR: ${e.message}`);
}

console.log("\n🎉 30-chapter Vampire data testing complete!");
console.log("All data files are properly formatted and ready for the application!");
